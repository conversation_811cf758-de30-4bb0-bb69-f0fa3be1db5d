#!/usr/bin/env julia

"""
綜合功能測試腳本 - 展示所有已實現的功能
包含：HTML報告生成、CSV導出、記憶體優化、平行處理
"""

using Dates

# 包含所有模組
include("src/LotteryAnalysis.jl")
using .LotteryAnalysis

include("src/BacktestingEngine.jl")
using .BacktestingEngine

function create_comprehensive_test_data()
    """創建綜合測試資料"""
    println("📊 創建測試資料...")
    
    # 生成不重複的隨機號碼
    function generate_unique_numbers()
        numbers = Int[]
        while length(numbers) < 5
            num = rand(1:39)
            if !(num in numbers)
                push!(numbers, num)
            end
        end
        return sort(numbers)
    end
    
    # 創建1000期測試資料
    test_data = [LotteryDraw(generate_unique_numbers(), Date(2023, 1, 1) + Day(i-1), i) for i in 1:1000]
    
    println("✅ 創建了 $(length(test_data)) 期測試資料")
    return test_data
end

function create_test_strategies()
    """創建測試策略"""
    println("🎯 創建測試策略...")
    
    strategies = SelectionStrategy[]
    
    # 策略1：頻率分析策略
    freq_criterion = BacktestingEngine.create_frequency_criterion(0.15, 0.35, 100)
    strategy1 = BacktestingEngine.SelectionStrategy("頻率分析策略", "基於號碼出現頻率的選號策略", [freq_criterion], BacktestingEngine.AND_LOGIC)
    push!(strategies, strategy1)

    # 策略2：間隔分析策略
    gap_criterion = BacktestingEngine.create_gap_criterion(5, 15, 50)
    strategy2 = BacktestingEngine.SelectionStrategy("間隔分析策略", "基於號碼間隔的選號策略", [gap_criterion], BacktestingEngine.AND_LOGIC)
    push!(strategies, strategy2)

    # 策略3：組合策略
    combined_criteria = [freq_criterion, gap_criterion]
    strategy3 = BacktestingEngine.SelectionStrategy("組合策略", "結合頻率和間隔分析的策略", combined_criteria, BacktestingEngine.OR_LOGIC)
    push!(strategies, strategy3)
    
    println("✅ 創建了 $(length(strategies)) 個測試策略")
    return strategies
end

function test_memory_optimization(strategies, test_data)
    """測試記憶體優化功能"""
    println("\n🧠 測試記憶體優化功能...")
    
    try
        # 測試記憶體高效模擬
        strategy = strategies[1]
        executions = simulate_strategy_memory_efficient(strategy, test_data, 200, 256.0)
        println("✅ 記憶體高效模擬: $(length(executions)) 次執行")
        
        # 測試串流處理
        streamer = create_data_streamer(test_data, 100)
        streaming_executions = simulate_strategy_chunked(strategy, streamer, 50)
        println("✅ 串流處理: $(length(streaming_executions)) 次執行")
        
        return true
    catch e
        println("❌ 記憶體優化測試失敗: $e")
        return false
    end
end

function test_parallel_processing(strategies, test_data)
    """測試平行處理功能"""
    println("\n⚡ 測試平行處理功能...")
    
    try
        # 配置平行處理
        parallel_config = configure_parallel_execution(max_threads=2, chunk_size=50, progress_reporting=false)
        
        # 測試平行策略回測
        strategy_results = simulate_strategies_parallel(strategies, test_data, parallel_config)
        println("✅ 平行策略回測: $(length(strategy_results)) 個策略結果")
        
        # 測試策略比較
        comparison = compare_strategies_parallel(strategies, test_data, parallel_config)
        println("✅ 平行策略比較: 最佳策略 $(comparison.best_performer)")
        
        return strategy_results, comparison
    catch e
        println("❌ 平行處理測試失敗: $e")
        return nothing, nothing
    end
end

function test_reporting_and_export(strategy_results, comparison)
    """測試報告生成和導出功能"""
    println("\n📋 測試報告生成和導出功能...")
    
    try
        # 測試HTML報告生成
        if strategy_results !== nothing && !isempty(strategy_results)
            first_strategy_name = collect(keys(strategy_results))[1]
            first_executions = strategy_results[first_strategy_name]
            
            if !isempty(first_executions)
                # 生成HTML報告
                reporter = HTMLReporter("綜合測試報告", "測試所有功能的綜合報告")
                report_path = generate_report(reporter, first_executions, "reports/comprehensive_test_report.html")
                println("✅ HTML報告生成: $report_path")
                
                # 測試CSV導出
                csv_exporter = CSVExporter("reports/comprehensive_test_results.csv")
                export_path = export_to_csv(csv_exporter, first_executions)
                println("✅ CSV導出: $export_path")
                
                return true
            else
                println("⚠️  沒有執行結果可供報告生成")
                return false
            end
        else
            println("⚠️  沒有策略結果可供報告生成")
            return false
        end
    catch e
        println("❌ 報告生成和導出測試失敗: $e")
        return false
    end
end

function test_performance_comparison()
    """測試效能比較"""
    println("\n📈 測試效能比較...")
    
    try
        # 創建小規模測試資料
        small_data = create_comprehensive_test_data()[1:100]
        strategies = create_test_strategies()
        
        # 測試串行執行
        println("  測試串行執行...")
        serial_start = time()
        serial_results = Dict{String, Vector{BacktestExecution}}()
        for strategy in strategies
            executions = simulate_strategy(strategy, small_data)
            serial_results[strategy.name] = executions
        end
        serial_time = time() - serial_start
        
        # 測試平行執行
        println("  測試平行執行...")
        parallel_start = time()
        parallel_config = configure_parallel_execution(max_threads=2, progress_reporting=false)
        parallel_results = simulate_strategies_parallel(strategies, small_data, parallel_config)
        parallel_time = time() - parallel_start
        
        # 比較效能
        speedup = serial_time / parallel_time
        println("✅ 效能比較:")
        println("  - 串行執行時間: $(round(serial_time, digits=2)) 秒")
        println("  - 平行執行時間: $(round(parallel_time, digits=2)) 秒")
        println("  - 加速比: $(round(speedup, digits=2))x")
        
        return true
    catch e
        println("❌ 效能比較測試失敗: $e")
        return false
    end
end

function main()
    println("🚀 開始綜合功能測試")
    println("="^60)
    println("測試範圍：HTML報告、CSV導出、記憶體優化、平行處理")
    println("="^60)
    
    # 創建測試資料和策略
    test_data = create_comprehensive_test_data()
    strategies = create_test_strategies()
    
    # 執行各項測試
    test_results = []
    
    # 1. 記憶體優化測試
    memory_success = test_memory_optimization(strategies, test_data)
    push!(test_results, ("記憶體優化", memory_success))
    
    # 2. 平行處理測試
    strategy_results, comparison = test_parallel_processing(strategies, test_data)
    parallel_success = strategy_results !== nothing
    push!(test_results, ("平行處理", parallel_success))
    
    # 3. 報告生成和導出測試
    report_success = test_reporting_and_export(strategy_results, comparison)
    push!(test_results, ("報告生成和導出", report_success))
    
    # 4. 效能比較測試
    performance_success = test_performance_comparison()
    push!(test_results, ("效能比較", performance_success))
    
    # 總結
    println("\n" * "="^60)
    println("📊 綜合測試結果總結:")
    
    passed = 0
    for (test_name, success) in test_results
        status = success ? "✅ 通過" : "❌ 失敗"
        println("  $test_name: $status")
        if success
            passed += 1
        end
    end
    
    total = length(test_results)
    println("\n🎯 總體結果: $passed/$total 個測試通過")
    
    if passed == total
        println("🎉 所有綜合測試通過！")
        println("💡 系統功能完整且運行正常")
        println("\n📋 已實現的功能:")
        println("  ✅ HTML報告生成器 (任務6.1)")
        println("  ✅ CSV導出功能 (任務6.2)")
        println("  ✅ 高效資料串流 (任務7.1)")
        println("  ✅ 平行處理支援 (任務7.2)")
        println("\n🎯 所有任務已完成！")
    else
        println("⚠️  部分綜合測試失敗，需要進一步調試")
    end
    
    println("\n" * "="^60)
end

# 執行綜合測試
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
