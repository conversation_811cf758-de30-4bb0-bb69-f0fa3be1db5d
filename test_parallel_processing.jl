#!/usr/bin/env julia

"""
平行處理功能測試腳本
"""

using Dates

# 手動包含必要的模組
include("src/LotteryAnalysis.jl")
using .LotteryAnalysis

include("src/ParallelProcessing.jl")
using .ParallelProcessing

include("src/BacktestingEngine.jl")
using .BacktestingEngine

# 明確導入需要的函數
using .ParallelProcessing: get_optimal_thread_count, configure_parallel_execution
using .ParallelProcessing: ThreadSafeDataStructure, safe_access, safe_modify, get_access_count
using .ParallelProcessing: create_thread_safe_results, add_result, add_results, get_results, get_progress
using .ParallelProcessing: ProgressAggregator, update_thread_progress, aggregate_progress, should_report_progress
using .ParallelProcessing: create_parallel_executor, execute_parallel_backtest

function test_parallel_configuration()
    println("🧪 測試平行處理配置...")
    
    try
        # 測試自動檢測執行緒數
        optimal_threads = get_optimal_thread_count()
        println("✅ 最佳執行緒數: $optimal_threads")
        
        # 測試配置創建
        config1 = configure_parallel_execution()
        println("✅ 預設配置: $(config1.max_threads) 執行緒, 分塊大小 $(config1.chunk_size)")
        
        config2 = configure_parallel_execution(max_threads=4, chunk_size=50, load_balancing=false)
        println("✅ 自定義配置: $(config2.max_threads) 執行緒, 分塊大小 $(config2.chunk_size)")
        
        # 測試無效配置
        try
            ParallelConfiguration(-1, 100)
            println("❌ 應該拋出錯誤但沒有")
            return false
        catch ArgumentError
            println("✅ 正確捕獲無效執行緒數錯誤")
        end
        
        return true
        
    catch e
        println("❌ 平行處理配置測試失敗: $e")
        return false
    end
end

function test_thread_safe_data_structure()
    println("\n🧪 測試執行緒安全資料結構...")
    
    try
        # 創建執行緒安全資料結構
        data = [1, 2, 3, 4, 5]
        ts_data = ThreadSafeDataStructure(data)
        
        # 測試安全存取
        result1 = safe_access(ts_data, d -> sum(d))
        println("✅ 安全存取結果: $result1")
        
        # 測試安全修改
        result2 = safe_modify(ts_data, d -> begin
            push!(d, 6)
            return length(d)
        end)
        println("✅ 安全修改結果: $result2")
        
        # 檢查存取計數
        access_count = get_access_count(ts_data)
        println("✅ 存取計數: $access_count")
        
        return true
        
    catch e
        println("❌ 執行緒安全資料結構測試失敗: $e")
        return false
    end
end

function test_thread_safe_results()
    println("\n🧪 測試執行緒安全結果收集器...")
    
    try
        # 創建結果收集器
        collector = create_thread_safe_results(Int, 10)
        
        # 添加單個結果
        add_result(collector, 42)
        println("✅ 添加單個結果")
        
        # 批次添加結果
        add_results(collector, [1, 2, 3, 4, 5])
        println("✅ 批次添加結果")
        
        # 獲取結果
        results = get_results(collector)
        println("✅ 獲取結果: $(length(results)) 個項目")
        
        # 獲取進度
        progress = get_progress(collector)
        println("✅ 進度: $(progress.completed)/$(progress.total) ($(round(progress.progress_ratio*100, digits=1))%)")
        
        return true
        
    catch e
        println("❌ 執行緒安全結果收集器測試失敗: $e")
        return false
    end
end

function test_progress_aggregator()
    println("\n🧪 測試進度聚合器...")
    
    try
        # 創建進度聚合器
        aggregator = ProgressAggregator(1.0)  # 1秒報告間隔
        
        # 模擬多個執行緒的進度
        for thread_id in 1:3
            progress = (
                completed = thread_id * 10,
                total = 100,
                progress_ratio = (thread_id * 10) / 100,
                elapsed_time = thread_id * 5.0,
                remaining_time = (100 - thread_id * 10) * 0.5
            )
            update_thread_progress(aggregator, thread_id, progress)
        end
        
        # 聚合進度
        overall_progress = aggregate_progress(aggregator)
        println("✅ 聚合進度: $(overall_progress.completed)/$(overall_progress.total), 活躍執行緒: $(overall_progress.active_threads)")
        
        # 測試報告間隔
        should_report1 = should_report_progress(aggregator)
        sleep(1.1)  # 等待超過報告間隔
        should_report2 = should_report_progress(aggregator)
        println("✅ 報告間隔測試: 第一次 $should_report1, 第二次 $should_report2")
        
        return true
        
    catch e
        println("❌ 進度聚合器測試失敗: $e")
        return false
    end
end

function test_parallel_executor()
    println("\n🧪 測試平行執行器...")
    
    try
        # 創建平行執行器
        executor = create_parallel_executor(max_threads=2, chunk_size=5, progress_reporting=false)
        
        # 創建測試任務
        tasks = [i for i in 1:20]
        
        # 定義處理函數
        function process_task(task::Int)
            # 模擬一些計算
            sleep(0.01)  # 短暫延遲
            return task * 2
        end
        
        # 執行平行處理
        results = execute_parallel_backtest(executor, tasks, process_task)
        
        println("✅ 平行執行完成: $(length(results)) 個結果")
        
        # 檢查結果
        expected_sum = sum(i * 2 for i in 1:20)
        actual_sum = sum(filter(x -> x !== nothing, results))
        
        if expected_sum == actual_sum
            println("✅ 結果驗證通過: 期望 $expected_sum, 實際 $actual_sum")
        else
            println("❌ 結果驗證失敗: 期望 $expected_sum, 實際 $actual_sum")
            return false
        end
        
        # 獲取執行統計
        stats = ParallelProcessing.get_execution_statistics(executor)
        println("✅ 執行統計: $(stats.max_threads) 執行緒, 分塊大小 $(stats.chunk_size)")
        
        return true
        
    catch e
        println("❌ 平行執行器測試失敗: $e")
        return false
    end
end

function test_parallel_strategy_simulation()
    println("\n🧪 測試平行策略模擬...")
    
    try
        # 創建測試策略
        strategies = SelectionStrategy[]
        
        for i in 1:3
            freq_criterion = create_frequency_criterion(0.1 + i*0.05, 0.3 + i*0.05, 50)
            criteria = SelectionCriterion[freq_criterion]
            strategy_name = "測試策略$i"
            strategy_desc = "測試策略$(i)的描述"
            strategy = SelectionStrategy(strategy_name, strategy_desc, criteria, AND_LOGIC)
            push!(strategies, strategy)
        end
        
        # 創建測試資料（確保沒有重複號碼）
        function generate_unique_numbers()
            numbers = Int[]
            while length(numbers) < 5
                num = rand(1:39)
                if !(num in numbers)
                    push!(numbers, num)
                end
            end
            return sort(numbers)
        end
        
        test_data = [BacktestingEngine.LotteryAnalysis.LotteryDraw(generate_unique_numbers(), Date(2023, 1, 1) + Day(i-1), i) for i in 1:100]
        
        # 配置平行處理
        parallel_config = BacktestingEngine.configure_parallel_execution(max_threads=2, chunk_size=10, progress_reporting=false)
        
        # 執行平行策略回測
        strategy_results = BacktestingEngine.simulate_strategies_parallel(strategies, test_data, parallel_config)
        
        println("✅ 平行策略回測完成: $(length(strategy_results)) 個策略結果")
        
        # 檢查結果
        for (strategy_name, executions) in strategy_results
            println("  - $strategy_name: $(length(executions)) 次執行")
        end
        
        # 測試策略比較
        comparison = BacktestingEngine.compare_strategies_parallel(strategies, test_data, parallel_config)
        println("✅ 策略比較完成: 最佳策略 $(comparison.best_performer)")
        
        return true
        
    catch e
        println("❌ 平行策略模擬測試失敗: $e")
        println("錯誤詳情:")
        showerror(stdout, e, catch_backtrace())
        return false
    end
end

function test_parallel_chunked_simulation()
    println("\n🧪 測試平行分塊模擬...")
    
    try
        # 創建測試策略
        freq_criterion = create_frequency_criterion(0.1, 0.3, 50)
        criteria = SelectionCriterion[freq_criterion]
        strategy = SelectionStrategy("分塊測試策略", "用於分塊測試的策略", criteria, AND_LOGIC)
        
        # 創建測試資料
        function generate_unique_numbers()
            numbers = Int[]
            while length(numbers) < 5
                num = rand(1:39)
                if !(num in numbers)
                    push!(numbers, num)
                end
            end
            return sort(numbers)
        end
        
        test_data = [BacktestingEngine.LotteryAnalysis.LotteryDraw(generate_unique_numbers(), Date(2023, 1, 1) + Day(i-1), i) for i in 1:200]
        
        # 配置平行處理
        parallel_config = BacktestingEngine.configure_parallel_execution(max_threads=2, chunk_size=20, progress_reporting=false)
        
        # 執行平行分塊模擬
        executions = BacktestingEngine.simulate_strategy_parallel_chunked(strategy, test_data, 50, parallel_config)
        
        println("✅ 平行分塊模擬完成: $(length(executions)) 次執行")
        
        return true
        
    catch e
        println("❌ 平行分塊模擬測試失敗: $e")
        println("錯誤詳情:")
        showerror(stdout, e, catch_backtrace())
        return false
    end
end

function main()
    println("🚀 開始平行處理功能測試")
    println("="^50)
    
    # 執行各項測試
    tests = [
        ("平行處理配置", test_parallel_configuration),
        ("執行緒安全資料結構", test_thread_safe_data_structure),
        ("執行緒安全結果收集器", test_thread_safe_results),
        ("進度聚合器", test_progress_aggregator),
        ("平行執行器", test_parallel_executor),
        ("平行策略模擬", test_parallel_strategy_simulation),
        ("平行分塊模擬", test_parallel_chunked_simulation)
    ]
    
    results = []
    
    for (test_name, test_function) in tests
        success = test_function()
        push!(results, (test_name, success))
    end
    
    # 總結
    println("\n" * "="^50)
    println("📊 測試結果總結:")
    
    passed = 0
    for (test_name, success) in results
        status = success ? "✅ 通過" : "❌ 失敗"
        println("  $test_name: $status")
        if success
            passed += 1
        end
    end
    
    total = length(results)
    println("\n🎯 總體結果: $passed/$total 個測試通過")
    
    if passed == total
        println("🎉 所有測試通過！平行處理功能實現成功")
        println("💡 任務7.2已完成")
    else
        println("⚠️  部分測試失敗，需要進一步調試")
    end
end

# 執行測試
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
