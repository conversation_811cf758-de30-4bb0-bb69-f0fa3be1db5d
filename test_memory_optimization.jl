#!/usr/bin/env julia

"""
記憶體優化功能測試腳本
"""

using Dates

# 手動包含必要的模組
include("src/LotteryAnalysis.jl")
using .LotteryAnalysis

include("src/MemoryOptimization.jl")
using .MemoryOptimization

include("src/BacktestingEngine.jl")
using .BacktestingEngine

# 明確導入需要的函數
using .MemoryOptimization: MemoryMonitor, get_memory_stats, monitor_memory_usage, optimize_memory_usage
using .MemoryOptimization: LazyDataLoader, load_data, clear_cache
using .MemoryOptimization: ChunkedDataProcessor, process_chunked_data
using .MemoryOptimization: create_data_streamer, read_next_chunk, has_more_data, reset_streamer
using .MemoryOptimization: create_streaming_windows, process_streaming_window
using .MemoryOptimization: AutoMemoryOptimizer, auto_optimize, get_optimization_recommendations

function test_memory_monitor()
    println("🧪 測試記憶體監控功能...")
    
    try
        # 創建記憶體監控器
        monitor = MemoryMonitor(1024.0, 0.8, true, 0.5)
        
        # 獲取記憶體統計
        stats = get_memory_stats()
        println("✅ 當前記憶體使用量: $(round(stats.current_mb, digits=1))MB")
        
        # 測試記憶體監控
        needs_optimization = monitor_memory_usage(monitor)
        println("✅ 記憶體監控結果: $(needs_optimization ? "需要優化" : "正常")")
        
        # 測試記憶體優化
        optimize_memory_usage()
        println("✅ 記憶體優化完成")
        
        return true
        
    catch e
        println("❌ 記憶體監控測試失敗: $e")
        return false
    end
end

function test_lazy_data_loader()
    println("\n🧪 測試延遲載入功能...")
    
    try
        # 創建測試資料載入函數
        function load_test_data(source::String)
            println("  載入資料: $source")
            return [i for i in 1:100]
        end
        
        # 創建延遲載入器
        loader = LazyDataLoader{Vector{Int}}("test_data", load_test_data, 50)
        
        # 第一次載入
        data1 = load_data(loader)
        println("✅ 第一次載入: $(length(data1)) 個項目")
        
        # 第二次載入（應該使用快取）
        data2 = load_data(loader)
        println("✅ 第二次載入: $(length(data2)) 個項目")
        
        # 清除快取
        clear_cache(loader)
        println("✅ 快取已清除")
        
        return true
        
    catch e
        println("❌ 延遲載入測試失敗: $e")
        return false
    end
end

function test_chunked_data_processor()
    println("\n🧪 測試分塊資料處理功能...")
    
    try
        # 創建測試資料
        test_data = [i for i in 1:1000]
        
        # 創建處理函數
        function process_chunk(chunk::Vector{Int})
            return sum(chunk)  # 簡單的求和處理
        end
        
        # 創建分塊處理器
        monitor = MemoryMonitor(512.0, 0.8, false)  # 關閉自動優化以便測試
        processor = ChunkedDataProcessor(100, 10, monitor, process_chunk)
        
        # 處理資料
        results = process_chunked_data(processor, test_data, vcat)
        
        println("✅ 分塊處理完成: $(length(results)) 個結果")
        println("✅ 結果總和: $(sum(results))")
        
        return true
        
    catch e
        println("❌ 分塊處理測試失敗: $e")
        return false
    end
end

function test_data_streamer()
    println("\n🧪 測試資料串流功能...")
    
    try
        # 創建測試資料
        test_data = [LotteryAnalysis.LotteryDraw([1, 2, 3, 4, 5], Date(2023, 1, 1) + Day(i-1), i) for i in 1:500]
        
        # 創建資料串流器
        streamer = create_data_streamer(test_data, 50)
        
        total_read = 0
        chunk_count = 0
        
        # 串流讀取資料
        while has_more_data(streamer)
            chunk = read_next_chunk(streamer)
            if !isempty(chunk)
                total_read += length(chunk)
                chunk_count += 1
            end
        end
        
        println("✅ 串流讀取完成: $chunk_count 個分塊，共 $total_read 個項目")
        
        # 重置串流器
        reset_streamer(streamer)
        println("✅ 串流器已重置")
        
        return true
        
    catch e
        println("❌ 資料串流測試失敗: $e")
        return false
    end
end

function test_streaming_backtest_windows()
    println("\n🧪 測試串流回測視窗功能...")
    
    try
        # 創建測試資料
        test_data = [BacktestingEngine.LotteryAnalysis.LotteryDraw([1, 2, 3, 4, 5], Date(2023, 1, 1) + Day(i-1), i) for i in 1:200]
        
        # 創建串流回測視窗
        windows = create_streaming_windows(test_data, 50, 10, 5, 20)
        
        println("✅ 創建了 $(length(windows)) 個串流回測視窗")
        
        # 測試處理第一個視窗
        if !isempty(windows)
            function test_processing(training_chunk, validation_chunk)
                return length(training_chunk) + length(validation_chunk)
            end
            
            results = process_streaming_window(windows[1], test_processing)
            println("✅ 第一個視窗處理完成: $(length(results)) 個結果")
        end
        
        return true
        
    catch e
        println("❌ 串流回測視窗測試失敗: $e")
        return false
    end
end

function test_auto_memory_optimizer()
    println("\n🧪 測試自動記憶體優化功能...")
    
    try
        # 創建自動優化器
        optimizer = AutoMemoryOptimizer(512.0)
        
        # 執行自動優化
        result = auto_optimize(optimizer)
        println("✅ 自動優化完成: 策略 $(result.strategy)")
        
        # 獲取優化建議
        recommendations = get_optimization_recommendations(optimizer)
        println("✅ 優化建議:")
        for (i, rec) in enumerate(recommendations)
            println("  $i. $rec")
        end
        
        return true
        
    catch e
        println("❌ 自動記憶體優化測試失敗: $e")
        return false
    end
end

function test_memory_efficient_simulation()
    println("\n🧪 測試記憶體高效策略模擬...")
    
    try
        # 創建測試策略
        freq_criterion = create_frequency_criterion(0.1, 0.3, 50)
        criteria = SelectionCriterion[freq_criterion]
        strategy = SelectionStrategy("記憶體測試策略", "用於測試記憶體優化的策略", criteria, AND_LOGIC)
        
        # 創建測試資料（確保沒有重複號碼）
        function generate_unique_numbers()
            numbers = Int[]
            while length(numbers) < 5
                num = rand(1:39)
                if !(num in numbers)
                    push!(numbers, num)
                end
            end
            return sort(numbers)
        end

        test_data = [BacktestingEngine.LotteryAnalysis.LotteryDraw(generate_unique_numbers(), Date(2023, 1, 1) + Day(i-1), i) for i in 1:1000]
        
        # 執行記憶體高效模擬
        executions = simulate_strategy_memory_efficient(strategy, test_data, 100, 256.0)
        
        println("✅ 記憶體高效模擬完成: $(length(executions)) 次執行")
        
        # 測試串流模擬
        streamer = BacktestingEngine.create_data_streamer(test_data, 50)
        streaming_executions = BacktestingEngine.simulate_strategy_chunked(strategy, streamer, 20)
        
        println("✅ 串流模擬完成: $(length(streaming_executions)) 次執行")
        
        return true
        
    catch e
        println("❌ 記憶體高效模擬測試失敗: $e")
        println("錯誤詳情:")
        showerror(stdout, e, catch_backtrace())
        return false
    end
end

function main()
    println("🚀 開始記憶體優化功能測試")
    println("="^50)
    
    # 執行各項測試
    tests = [
        ("記憶體監控", test_memory_monitor),
        ("延遲載入", test_lazy_data_loader),
        ("分塊處理", test_chunked_data_processor),
        ("資料串流", test_data_streamer),
        ("串流回測視窗", test_streaming_backtest_windows),
        ("自動記憶體優化", test_auto_memory_optimizer),
        ("記憶體高效模擬", test_memory_efficient_simulation)
    ]
    
    results = []
    
    for (test_name, test_function) in tests
        success = test_function()
        push!(results, (test_name, success))
    end
    
    # 總結
    println("\n" * "="^50)
    println("📊 測試結果總結:")
    
    passed = 0
    for (test_name, success) in results
        status = success ? "✅ 通過" : "❌ 失敗"
        println("  $test_name: $status")
        if success
            passed += 1
        end
    end
    
    total = length(results)
    println("\n🎯 總體結果: $passed/$total 個測試通過")
    
    if passed == total
        println("🎉 所有測試通過！記憶體優化功能實現成功")
        println("💡 任務7.1已完成")
    else
        println("⚠️  部分測試失敗，需要進一步調試")
    end
end

# 執行測試
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
