module ReportingEngine

using Dates, Statistics, Printf

# 嘗試載入圖表包
const PLOTS_AVAILABLE = try
    using Plots
    true
catch
    false
end

export HTMLReporter, CSVExporter
export generate_html_report, generate_csv_export, generate_comparison_report

# ============================================================================
# HTML報告生成器 (Task 6.1)
# ============================================================================

"""
    HTMLReporter

HTML報告生成器，支援模板化報告生成、圖表整合和互動元素。
"""
struct HTMLReporter
    template_style::String
    include_charts::Bool
    interactive_elements::Bool
    custom_css::String
    chart_config::Dict{String, Any}
    
    function HTMLReporter(template_style::String="professional",
                         include_charts::Bool=true,
                         interactive_elements::Bool=true,
                         custom_css::String="",
                         chart_config::Dict{String, Any}=Dict{String, Any}())
        valid_styles = ["professional", "simple", "detailed"]
        if !(template_style in valid_styles)
            throw(ArgumentError("無效的模板樣式: $template_style. 可用選項: $(join(valid_styles, ", "))"))
        end
        
        # 預設圖表配置
        default_chart_config = Dict{String, Any}(
            "width" => 1000,
            "height" => 600,
            "theme" => "plotly_white",
            "show_legend" => true,
            "grid" => true
        )
        
        # 合併用戶配置
        merged_config = merge(default_chart_config, chart_config)
        
        new(template_style, include_charts, interactive_elements, custom_css, merged_config)
    end
end

"""
    CSVExporter

CSV導出器，支援結構化CSV格式、批次導出和資料過濾。
"""
struct CSVExporter
    delimiter::String
    include_headers::Bool
    date_format::String
    number_format::String
    encoding::String
    
    function CSVExporter(delimiter::String=",",
                        include_headers::Bool=true,
                        date_format::String="yyyy-mm-dd",
                        number_format::String="%.4f",
                        encoding::String="UTF-8")
        new(delimiter, include_headers, date_format, number_format, encoding)
    end
end

"""
    generate_html_report(reporter::HTMLReporter, results, executions, filename::String)

生成完整的HTML回測報告。
"""
function generate_html_report(reporter::HTMLReporter, 
                             results,
                             executions,
                             filename::String)::String
    if isempty(executions)
        throw(ArgumentError("執行結果不能為空"))
    end
    
    # 確保檔案名稱有.html副檔名
    if !endswith(filename, ".html")
        filename = filename * ".html"
    end
    
    # 生成HTML內容
    html_content = build_html_content(reporter, results, executions)
    
    # 寫入檔案
    try
        open(filename, "w") do f
            write(f, html_content)
        end
        @info "HTML報告已生成: $filename"
        return filename
    catch e
        @error "生成HTML報告失敗: $e"
        rethrow(e)
    end
end

"""
    build_html_content(reporter::HTMLReporter, results, executions)

構建完整的HTML內容。
"""
function build_html_content(reporter::HTMLReporter, results, executions)::String
    # HTML文檔結構
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>回測報告 - $(results.strategy_name)</title>
        $(generate_css_styles(reporter))
        $(generate_javascript_includes(reporter))
    </head>
    <body>
        <div class="container">
            $(generate_header_section(results))
            $(generate_summary_section(results))
            $(generate_performance_metrics_section(results))
            $(generate_execution_details_section(executions, reporter))
            $(generate_charts_section(results, executions, reporter))
            $(generate_footer_section())
        </div>
        $(generate_javascript_code(reporter))
    </body>
    </html>
    """
    
    return html_content
end

"""
    generate_css_styles(reporter::HTMLReporter)

生成CSS樣式。
"""
function generate_css_styles(reporter::HTMLReporter)::String
    base_css = """
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #007bff;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: #666;
            font-size: 1.2em;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 8px;
            background-color: #fafafa;
            border-left: 4px solid #007bff;
        }
        
        .section h2 {
            color: #007bff;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #ddd;
            margin-top: 40px;
        }
    </style>
    """
    
    # 添加自定義CSS
    if !isempty(reporter.custom_css)
        base_css *= "\n<style>\n$(reporter.custom_css)\n</style>"
    end
    
    return base_css
end

"""
    generate_javascript_includes(reporter::HTMLReporter)

生成JavaScript包含文件。
"""
function generate_javascript_includes(reporter::HTMLReporter)::String
    if !reporter.interactive_elements && !reporter.include_charts
        return ""
    end
    
    includes = ""
    
    # 如果需要圖表，包含Plotly.js
    if reporter.include_charts
        includes *= """
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        """
    end
    
    return includes
end

"""
    generate_header_section(results)

生成報告標題部分。
"""
function generate_header_section(results)::String
    start_date, end_date = results.execution_period

    return """
    <div class="header">
        <h1>📊 回測報告</h1>
        <div class="subtitle">
            <strong>策略名稱:</strong> $(results.strategy_name)<br>
            <strong>測試期間:</strong> $(start_date) 至 $(end_date)<br>
            <strong>生成時間:</strong> $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))
        </div>
    </div>
    """
end

"""
    generate_summary_section(results)

生成摘要部分。
"""
function generate_summary_section(results)::String
    roi_class = results.roi > 0 ? "positive" : (results.roi < 0 ? "negative" : "neutral")
    hit_rate_class = results.hit_rate > 0.2 ? "positive" : (results.hit_rate > 0.1 ? "neutral" : "negative")

    return """
    <div class="section">
        <h2>📈 執行摘要</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">$(results.total_executions)</div>
                <div class="metric-label">總執行次數</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">$(results.total_hits)</div>
                <div class="metric-label">總命中數</div>
            </div>
            <div class="metric-card">
                <div class="metric-value $hit_rate_class">$(round(results.hit_rate * 100, digits=2))%</div>
                <div class="metric-label">命中率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value $roi_class">$(round(results.roi * 100, digits=2))%</div>
                <div class="metric-label">投資報酬率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">$(round(results.average_hits_per_execution, digits=2))</div>
                <div class="metric-label">平均每次命中數</div>
            </div>
            <div class="metric-card">
                <div class="metric-value negative">$(results.max_consecutive_misses)</div>
                <div class="metric-label">最大連續未中</div>
            </div>
        </div>
    </div>
    """
end

"""
    generate_performance_metrics_section(results)

生成詳細績效指標部分。
"""
function generate_performance_metrics_section(results)::String
    # 命中分佈表格
    hit_dist_rows = ""
    for hits in 0:5
        count = get(results.hit_distribution, hits, 0)
        percentage = results.total_executions > 0 ? round(count / results.total_executions * 100, digits=2) : 0.0
        hit_dist_rows *= """
        <tr>
            <td>$hits 個命中</td>
            <td>$count</td>
            <td>$percentage%</td>
        </tr>
        """
    end

    return """
    <div class="section">
        <h2>📊 詳細績效指標</h2>

        <h3>💰 財務指標</h3>
        <div class="table-container">
            <table>
                <tr><th>指標</th><th>數值</th><th>說明</th></tr>
                <tr><td>總投資</td><td>\$$(round(results.total_investment, digits=2))</td><td>假設每次執行成本為1單位</td></tr>
                <tr><td>總回報</td><td>\$$(round(results.total_return, digits=2))</td><td>根據標準彩票賠率計算</td></tr>
                <tr><td>淨利潤</td><td>\$$(round(results.total_return - results.total_investment, digits=2))</td><td>總回報減去總投資</td></tr>
                <tr><td>投資報酬率</td><td>$(round(results.roi * 100, digits=2))%</td><td>淨利潤除以總投資</td></tr>
            </table>
        </div>

        <h3>🎯 命中分佈</h3>
        <div class="table-container">
            <table>
                <tr><th>命中數</th><th>次數</th><th>百分比</th></tr>
                $hit_dist_rows
            </table>
        </div>

        <h3>📈 連續性指標</h3>
        <div class="table-container">
            <table>
                <tr><th>指標</th><th>數值</th><th>說明</th></tr>
                <tr><td>最大連續命中</td><td>$(results.max_consecutive_hits)</td><td>連續有命中的最長期數</td></tr>
                <tr><td>最大連續未中</td><td>$(results.max_consecutive_misses)</td><td>連續無命中的最長期數</td></tr>
                <tr><td>最佳連續期開始</td><td>第$(results.best_streak_start)期</td><td>最佳連續命中期的開始</td></tr>
                <tr><td>最差連續期開始</td><td>第$(results.worst_streak_start)期</td><td>最差連續未中期的開始</td></tr>
            </table>
        </div>
    </div>
    """
end

"""
    generate_execution_details_section(executions, reporter::HTMLReporter)

生成執行詳情部分。
"""
function generate_execution_details_section(executions, reporter::HTMLReporter)::String
    # 限制顯示的執行數量以避免頁面過大
    max_display = 50
    display_executions = length(executions) > max_display ? executions[end-max_display+1:end] : executions

    execution_rows = ""
    for (i, exec) in enumerate(display_executions)
        hit_class = exec.hits > 2 ? "positive" : (exec.hits > 0 ? "neutral" : "negative")
        selected_nums = join(exec.selected_numbers, ", ")
        actual_nums = join(exec.actual_draw.numbers, ", ")
        confidence_avg = isempty(exec.confidence_scores) ? 0.0 :
                        round(mean(values(exec.confidence_scores)), digits=3)

        execution_rows *= """
        <tr>
            <td>$(exec.execution_date)</td>
            <td>$selected_nums</td>
            <td>$actual_nums</td>
            <td class="$hit_class">$(exec.hits)</td>
            <td>$confidence_avg</td>
            <td>$(round(exec.execution_time * 1000, digits=1))ms</td>
        </tr>
        """
    end

    total_shown = length(display_executions)
    total_executions = length(executions)

    return """
    <div class="section">
        <h2>📋 執行詳情</h2>
        <p><strong>顯示最近 $total_shown 次執行</strong> (總共 $total_executions 次)</p>

        <div class="table-container">
            <table>
                <tr>
                    <th>執行日期</th>
                    <th>選擇號碼</th>
                    <th>實際開獎</th>
                    <th>命中數</th>
                    <th>平均信心度</th>
                    <th>執行時間</th>
                </tr>
                $execution_rows
            </table>
        </div>
    </div>
    """
end

"""
    generate_charts_section(results, executions, reporter::HTMLReporter)

生成圖表部分。
"""
function generate_charts_section(results, executions, reporter::HTMLReporter)::String
    if !reporter.include_charts
        return ""
    end

    return """
    <div class="section">
        <h2>📈 視覺化分析</h2>
        <div class="chart-container">
            <p>⚠️ 圖表功能需要安裝 Plots.jl 套件</p>
            <p>安裝命令: <code>julia -e "using Pkg; Pkg.add(\\"Plots\\")"</code></p>
        </div>
    </div>
    """
end

"""
    generate_footer_section()

生成頁腳部分。
"""
function generate_footer_section()::String
    return """
    <div class="footer">
        <p>📊 由 AmibrokerChartSimilator 回測引擎生成</p>
        <p>生成時間: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))</p>
    </div>
    """
end

"""
    generate_javascript_code(reporter::HTMLReporter)

生成JavaScript代碼。
"""
function generate_javascript_code(reporter::HTMLReporter)::String
    if !reporter.interactive_elements
        return ""
    end

    return """
    <script>
        // 基本的互動功能
        console.log('回測報告已載入');
    </script>
    """
end

"""
    generate_csv_export(exporter::CSVExporter, results, executions, filename::String)

生成CSV格式的回測結果導出。
"""
function generate_csv_export(exporter::CSVExporter,
                            results,
                            executions,
                            filename::String)::String
    if isempty(executions)
        throw(ArgumentError("執行結果不能為空"))
    end

    # 確保檔案名稱有.csv副檔名
    if !endswith(filename, ".csv")
        filename = filename * ".csv"
    end

    # 生成CSV內容
    csv_content = build_csv_content(exporter, results, executions)

    # 寫入檔案
    try
        open(filename, "w") do f
            write(f, csv_content)
        end
        @info "CSV導出已完成: $filename"
        return filename
    catch e
        @error "生成CSV導出失敗: $e"
        rethrow(e)
    end
end

"""
    build_csv_content(exporter::CSVExporter, results, executions)

構建CSV內容。
"""
function build_csv_content(exporter::CSVExporter, results, executions)::String
    lines = String[]

    # 添加標題行
    if exporter.include_headers
        headers = [
            "執行日期", "選擇號碼1", "選擇號碼2", "選擇號碼3", "選擇號碼4", "選擇號碼5",
            "實際號碼1", "實際號碼2", "實際號碼3", "實際號碼4", "實際號碼5",
            "命中數", "命中位置", "平均信心度", "執行時間(秒)", "策略名稱"
        ]
        push!(lines, join(headers, exporter.delimiter))
    end

    # 添加執行資料
    for exec in executions
        # 格式化日期
        date_str = Dates.format(exec.execution_date, exporter.date_format)

        # 選擇的號碼 (補齊到5個)
        selected = vcat(exec.selected_numbers, fill(0, 5 - length(exec.selected_numbers)))[1:5]

        # 實際開獎號碼 (補齊到5個)
        actual = vcat(exec.actual_draw.numbers, fill(0, 5 - length(exec.actual_draw.numbers)))[1:5]

        # 命中位置
        hit_positions_str = isempty(exec.hit_positions) ? "" : join(exec.hit_positions, ";")

        # 平均信心度
        avg_confidence = isempty(exec.confidence_scores) ? 0.0 : mean(values(exec.confidence_scores))
        confidence_str = @sprintf("%.3f", avg_confidence)

        # 執行時間
        time_str = @sprintf("%.4f", exec.execution_time)

        # 組合資料行
        row_data = [
            date_str,
            string.(selected)...,
            string.(actual)...,
            string(exec.hits),
            hit_positions_str,
            confidence_str,
            time_str,
            exec.strategy.name
        ]

        push!(lines, join(row_data, exporter.delimiter))
    end

    return join(lines, "\n")
end

"""
    generate_comparison_report(reporter::HTMLReporter, comparison, filename::String)

生成策略比較HTML報告。
"""
function generate_comparison_report(reporter::HTMLReporter, comparison, filename::String)::String
    if !endswith(filename, ".html")
        filename = filename * ".html"
    end

    html_content = """
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>策略比較報告</title>
        $(generate_css_styles(reporter))
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔍 策略比較報告</h1>
                <div class="subtitle">
                    <strong>比較策略數:</strong> $(length(comparison.strategies))<br>
                    <strong>比較日期:</strong> $(comparison.comparison_date)<br>
                    <strong>推薦策略:</strong> $(comparison.best_performer)
                </div>
            </div>
            $(generate_footer_section())
        </div>
    </body>
    </html>
    """

    # 寫入檔案
    open(filename, "w") do f
        write(f, html_content)
    end

    @info "策略比較報告已生成: $filename"
    return filename
end

end # module
