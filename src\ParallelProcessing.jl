module ParallelProcessing

using Dates, Statistics
using Base.Threads

export ParallelBacktestExecutor, ThreadSafeResults, ProgressAggregator
export execute_parallel_backtest, create_thread_safe_results, aggregate_progress
export ParallelConfiguration, ThreadSafeDataStructure
export configure_parallel_execution, get_optimal_thread_count

# ============================================================================
# 平行處理配置 (Task 7.2)
# ============================================================================

"""
    ParallelConfiguration

平行處理配置，控制並發執行參數。

# Fields
- `max_threads::Int`: 最大執行緒數量
- `chunk_size::Int`: 每個執行緒處理的分塊大小
- `load_balancing::Bool`: 是否啟用負載平衡
- `progress_reporting::Bool`: 是否啟用進度報告
- `memory_limit_per_thread::Float64`: 每個執行緒的記憶體限制 (MB)
"""
struct ParallelConfiguration
    max_threads::Int
    chunk_size::Int
    load_balancing::Bool
    progress_reporting::Bool
    memory_limit_per_thread::Float64
    
    function ParallelConfiguration(max_threads::Int=0,
                                  chunk_size::Int=100,
                                  load_balancing::Bool=true,
                                  progress_reporting::Bool=true,
                                  memory_limit_per_thread::Float64=128.0)
        # 如果max_threads為0，自動檢測最佳執行緒數
        actual_threads = max_threads == 0 ? get_optimal_thread_count() : max_threads
        
        if actual_threads <= 0
            throw(ArgumentError("執行緒數量必須為正數: $actual_threads"))
        end
        if chunk_size <= 0
            throw(ArgumentError("分塊大小必須為正數: $chunk_size"))
        end
        if memory_limit_per_thread <= 0
            throw(ArgumentError("記憶體限制必須為正數: $memory_limit_per_thread"))
        end
        
        new(actual_threads, chunk_size, load_balancing, progress_reporting, memory_limit_per_thread)
    end
end

"""
    get_optimal_thread_count()

獲取最佳執行緒數量。

# Returns
- `Int`: 建議的執行緒數量
"""
function get_optimal_thread_count()::Int
    # 使用Julia的執行緒數量，但限制在合理範圍內
    available_threads = Threads.nthreads()
    
    # 考慮系統負載，通常使用CPU核心數的80%
    optimal_threads = max(1, min(available_threads, div(Sys.CPU_THREADS, 2)))
    
    @info "檢測到 $available_threads 個可用執行緒, 建議使用 $optimal_threads 個執行緒"
    
    return optimal_threads
end

"""
    configure_parallel_execution(;max_threads::Int=0, 
                                chunk_size::Int=100,
                                load_balancing::Bool=true,
                                progress_reporting::Bool=true,
                                memory_limit_per_thread::Float64=128.0)

配置平行執行參數。

# Returns
- `ParallelConfiguration`: 平行處理配置
"""
function configure_parallel_execution(;max_threads::Int=0, 
                                     chunk_size::Int=100,
                                     load_balancing::Bool=true,
                                     progress_reporting::Bool=true,
                                     memory_limit_per_thread::Float64=128.0)::ParallelConfiguration
    return ParallelConfiguration(max_threads, chunk_size, load_balancing, 
                               progress_reporting, memory_limit_per_thread)
end

# ============================================================================
# 執行緒安全資料結構 (Task 7.2)
# ============================================================================

"""
    ThreadSafeDataStructure{T}

執行緒安全的資料結構包裝器。

# Fields
- `data::T`: 包裝的資料
- `lock::ReentrantLock`: 執行緒鎖
- `access_count::Atomic{Int}`: 存取計數器
"""
mutable struct ThreadSafeDataStructure{T}
    data::T
    lock::ReentrantLock
    access_count::Atomic{Int}
    
    function ThreadSafeDataStructure{T}(data::T) where T
        new{T}(data, ReentrantLock(), Atomic{Int}(0))
    end
end

"""
    ThreadSafeDataStructure(data::T) where T

創建執行緒安全的資料結構。
"""
ThreadSafeDataStructure(data::T) where T = ThreadSafeDataStructure{T}(data)

"""
    safe_access(ts_data::ThreadSafeDataStructure{T}, func::Function) where T

安全存取執行緒安全資料結構。

# Arguments
- `ts_data::ThreadSafeDataStructure{T}`: 執行緒安全資料結構
- `func::Function`: 存取函數，接收資料作為參數

# Returns
- 函數執行結果
"""
function safe_access(ts_data::ThreadSafeDataStructure{T}, func::Function) where T
    lock(ts_data.lock) do
        atomic_add!(ts_data.access_count, 1)
        return func(ts_data.data)
    end
end

"""
    safe_modify(ts_data::ThreadSafeDataStructure{T}, func::Function) where T

安全修改執行緒安全資料結構。

# Arguments
- `ts_data::ThreadSafeDataStructure{T}`: 執行緒安全資料結構
- `func::Function`: 修改函數，接收資料作為參數並可能修改它

# Returns
- 函數執行結果
"""
function safe_modify(ts_data::ThreadSafeDataStructure{T}, func::Function) where T
    lock(ts_data.lock) do
        atomic_add!(ts_data.access_count, 1)
        return func(ts_data.data)
    end
end

"""
    get_access_count(ts_data::ThreadSafeDataStructure)

獲取存取計數。

# Returns
- `Int`: 存取次數
"""
function get_access_count(ts_data::ThreadSafeDataStructure)::Int
    return ts_data.access_count[]
end

# ============================================================================
# 執行緒安全結果收集器 (Task 7.2)
# ============================================================================

"""
    ThreadSafeResults{T}

執行緒安全的結果收集器。

# Fields
- `results::Vector{T}`: 結果列表
- `lock::ReentrantLock`: 執行緒鎖
- `completed_tasks::Atomic{Int}`: 已完成任務數
- `total_tasks::Int`: 總任務數
- `start_time::Float64`: 開始時間
"""
mutable struct ThreadSafeResults{T}
    results::Vector{T}
    lock::ReentrantLock
    completed_tasks::Atomic{Int}
    total_tasks::Int
    start_time::Float64
    
    function ThreadSafeResults{T}(total_tasks::Int) where T
        new{T}(T[], ReentrantLock(), Atomic{Int}(0), total_tasks, time())
    end
end

"""
    create_thread_safe_results(::Type{T}, total_tasks::Int) where T

創建執行緒安全的結果收集器。

# Arguments
- `T`: 結果類型
- `total_tasks::Int`: 總任務數

# Returns
- `ThreadSafeResults{T}`: 執行緒安全結果收集器
"""
function create_thread_safe_results(::Type{T}, total_tasks::Int) where T
    return ThreadSafeResults{T}(total_tasks)
end

"""
    add_result(ts_results::ThreadSafeResults{T}, result::T) where T

添加結果到執行緒安全收集器。

# Arguments
- `ts_results::ThreadSafeResults{T}`: 結果收集器
- `result::T`: 要添加的結果
"""
function add_result(ts_results::ThreadSafeResults{T}, result::T) where T
    lock(ts_results.lock) do
        push!(ts_results.results, result)
        atomic_add!(ts_results.completed_tasks, 1)
    end
end

"""
    add_results(ts_results::ThreadSafeResults{T}, results::Vector{T}) where T

批次添加結果到執行緒安全收集器。

# Arguments
- `ts_results::ThreadSafeResults{T}`: 結果收集器
- `results::Vector{T}`: 要添加的結果列表
"""
function add_results(ts_results::ThreadSafeResults{T}, results::Vector{T}) where T
    lock(ts_results.lock) do
        append!(ts_results.results, results)
        atomic_add!(ts_results.completed_tasks, length(results))
    end
end

"""
    get_results(ts_results::ThreadSafeResults{T}) where T

獲取所有結果。

# Returns
- `Vector{T}`: 結果列表的副本
"""
function get_results(ts_results::ThreadSafeResults{T}) where T
    lock(ts_results.lock) do
        return copy(ts_results.results)
    end
end

"""
    get_progress(ts_results::ThreadSafeResults)

獲取執行進度。

# Returns
- `NamedTuple`: 包含進度信息的命名元組
"""
function get_progress(ts_results::ThreadSafeResults)
    completed = ts_results.completed_tasks[]
    total = ts_results.total_tasks
    elapsed_time = time() - ts_results.start_time
    
    progress_ratio = total > 0 ? completed / total : 0.0
    estimated_total_time = progress_ratio > 0 ? elapsed_time / progress_ratio : 0.0
    remaining_time = max(0.0, estimated_total_time - elapsed_time)
    
    return (
        completed = completed,
        total = total,
        progress_ratio = progress_ratio,
        elapsed_time = elapsed_time,
        estimated_total_time = estimated_total_time,
        remaining_time = remaining_time
    )
end

# ============================================================================
# 進度聚合器 (Task 7.2)
# ============================================================================

"""
    ProgressAggregator

進度聚合器，收集和報告多個執行緒的進度。

# Fields
- `thread_progress::Dict{Int, NamedTuple}`: 各執行緒進度
- `lock::ReentrantLock`: 執行緒鎖
- `reporting_interval::Float64`: 報告間隔 (秒)
- `last_report_time::Float64`: 上次報告時間
"""
mutable struct ProgressAggregator
    thread_progress::Dict{Int, NamedTuple}
    lock::ReentrantLock
    reporting_interval::Float64
    last_report_time::Float64
    
    function ProgressAggregator(reporting_interval::Float64=5.0)
        new(Dict{Int, NamedTuple}(), ReentrantLock(), reporting_interval, time())
    end
end

"""
    update_thread_progress(aggregator::ProgressAggregator, 
                          thread_id::Int, 
                          progress::NamedTuple)

更新執行緒進度。

# Arguments
- `aggregator::ProgressAggregator`: 進度聚合器
- `thread_id::Int`: 執行緒ID
- `progress::NamedTuple`: 進度信息
"""
function update_thread_progress(aggregator::ProgressAggregator, 
                               thread_id::Int, 
                               progress::NamedTuple)
    lock(aggregator.lock) do
        aggregator.thread_progress[thread_id] = progress
    end
end

"""
    aggregate_progress(aggregator::ProgressAggregator)

聚合所有執行緒的進度。

# Returns
- `NamedTuple`: 聚合的進度信息
"""
function aggregate_progress(aggregator::ProgressAggregator)
    lock(aggregator.lock) do
        if isempty(aggregator.thread_progress)
            return (completed=0, total=0, progress_ratio=0.0, 
                   elapsed_time=0.0, remaining_time=0.0, active_threads=0)
        end
        
        total_completed = sum(p.completed for p in values(aggregator.thread_progress))
        total_tasks = sum(p.total for p in values(aggregator.thread_progress))
        max_elapsed = maximum(p.elapsed_time for p in values(aggregator.thread_progress))
        active_threads = length(aggregator.thread_progress)
        
        progress_ratio = total_tasks > 0 ? total_completed / total_tasks : 0.0
        estimated_total_time = progress_ratio > 0 ? max_elapsed / progress_ratio : 0.0
        remaining_time = max(0.0, estimated_total_time - max_elapsed)
        
        return (
            completed = total_completed,
            total = total_tasks,
            progress_ratio = progress_ratio,
            elapsed_time = max_elapsed,
            remaining_time = remaining_time,
            active_threads = active_threads
        )
    end
end

"""
    should_report_progress(aggregator::ProgressAggregator)

檢查是否應該報告進度。

# Returns
- `Bool`: 是否應該報告
"""
function should_report_progress(aggregator::ProgressAggregator)::Bool
    current_time = time()
    if current_time - aggregator.last_report_time >= aggregator.reporting_interval
        aggregator.last_report_time = current_time
        return true
    end
    return false
end

# ============================================================================
# 平行回測執行器 (Task 7.2)
# ============================================================================

"""
    ParallelBacktestExecutor

平行回測執行器，管理多執行緒回測執行。

# Fields
- `config::ParallelConfiguration`: 平行處理配置
- `progress_aggregator::ProgressAggregator`: 進度聚合器
- `active_tasks::Atomic{Int}`: 活躍任務數
"""
struct ParallelBacktestExecutor
    config::ParallelConfiguration
    progress_aggregator::ProgressAggregator
    active_tasks::Atomic{Int}

    function ParallelBacktestExecutor(config::ParallelConfiguration)
        aggregator = ProgressAggregator(5.0)  # 5秒報告間隔
        new(config, aggregator, Atomic{Int}(0))
    end
end

"""
    execute_parallel_backtest(executor::ParallelBacktestExecutor,
                             tasks::Vector{T},
                             processing_function::Function) where T

執行平行回測。

# Arguments
- `executor::ParallelBacktestExecutor`: 平行執行器
- `tasks::Vector{T}`: 要處理的任務列表
- `processing_function::Function`: 處理函數，接收任務並返回結果

# Returns
- `Vector`: 所有結果的聚合
"""
function execute_parallel_backtest(executor::ParallelBacktestExecutor,
                                  tasks::Vector{T},
                                  processing_function::Function) where T
    if isempty(tasks)
        @warn "沒有任務需要執行"
        return []
    end

    total_tasks = length(tasks)
    @info "開始平行回測執行: $total_tasks 個任務, $(executor.config.max_threads) 個執行緒"

    # 創建執行緒安全結果收集器
    results_collector = create_thread_safe_results(Any, total_tasks)

    # 分割任務到各個執行緒
    task_chunks = create_task_chunks(tasks, executor.config)

    # 啟動平行執行
    @threads for chunk_idx in 1:length(task_chunks)
        execute_chunk(executor, task_chunks[chunk_idx], processing_function,
                     results_collector, chunk_idx)
    end

    # 等待所有任務完成並返回結果
    final_results = get_results(results_collector)
    final_progress = get_progress(results_collector)

    @info "平行回測執行完成: $(final_progress.completed)/$(final_progress.total) 個任務, 耗時 $(round(final_progress.elapsed_time, digits=2)) 秒"

    return final_results
end

"""
    create_task_chunks(tasks::Vector{T}, config::ParallelConfiguration) where T

將任務分割成適合平行處理的分塊。

# Arguments
- `tasks::Vector{T}`: 任務列表
- `config::ParallelConfiguration`: 平行處理配置

# Returns
- `Vector{Vector{T}}`: 任務分塊列表
"""
function create_task_chunks(tasks::Vector{T}, config::ParallelConfiguration) where T
    total_tasks = length(tasks)

    if config.load_balancing
        # 動態負載平衡：創建更多小分塊
        chunk_size = min(config.chunk_size, max(1, div(total_tasks, config.max_threads * 2)))
    else
        # 靜態分割：平均分配
        chunk_size = max(1, div(total_tasks, config.max_threads))
    end

    chunks = Vector{T}[]
    start_idx = 1

    while start_idx <= total_tasks
        end_idx = min(start_idx + chunk_size - 1, total_tasks)
        push!(chunks, tasks[start_idx:end_idx])
        start_idx = end_idx + 1
    end

    @info "任務分割完成: $(length(chunks)) 個分塊, 平均每塊 $(round(total_tasks/length(chunks), digits=1)) 個任務"

    return chunks
end

"""
    execute_chunk(executor::ParallelBacktestExecutor,
                 chunk::Vector{T},
                 processing_function::Function,
                 results_collector::ThreadSafeResults,
                 chunk_id::Int) where T

執行單個任務分塊。

# Arguments
- `executor::ParallelBacktestExecutor`: 執行器
- `chunk::Vector{T}`: 任務分塊
- `processing_function::Function`: 處理函數
- `results_collector::ThreadSafeResults`: 結果收集器
- `chunk_id::Int`: 分塊ID
"""
function execute_chunk(executor::ParallelBacktestExecutor,
                      chunk::Vector{T},
                      processing_function::Function,
                      results_collector::ThreadSafeResults,
                      chunk_id::Int) where T
    thread_id = Threads.threadid()
    atomic_add!(executor.active_tasks, 1)

    try
        @info "執行緒 $thread_id 開始處理分塊 $chunk_id ($(length(chunk)) 個任務)"

        chunk_results = []
        start_time = time()

        for (task_idx, task) in enumerate(chunk)
            try
                # 執行處理函數
                result = processing_function(task)
                push!(chunk_results, result)

                # 更新進度
                if executor.config.progress_reporting
                    elapsed = time() - start_time
                    progress = (
                        completed = task_idx,
                        total = length(chunk),
                        progress_ratio = task_idx / length(chunk),
                        elapsed_time = elapsed,
                        remaining_time = elapsed * (length(chunk) - task_idx) / task_idx
                    )

                    update_thread_progress(executor.progress_aggregator, thread_id, progress)

                    # 定期報告總體進度
                    if should_report_progress(executor.progress_aggregator)
                        overall_progress = aggregate_progress(executor.progress_aggregator)
                        @info "總體進度: $(round(overall_progress.progress_ratio * 100, digits=1))% ($(overall_progress.completed)/$(overall_progress.total)), 活躍執行緒: $(overall_progress.active_threads)"
                    end
                end

            catch e
                @warn "任務處理失敗 (執行緒 $thread_id, 分塊 $chunk_id, 任務 $task_idx): $e"
                # 可以選擇添加錯誤結果或跳過
                push!(chunk_results, nothing)
            end
        end

        # 將分塊結果添加到總結果中
        add_results(results_collector, chunk_results)

        @info "執行緒 $thread_id 完成分塊 $chunk_id: $(length(chunk_results)) 個結果"

    finally
        atomic_add!(executor.active_tasks, -1)
    end
end

"""
    create_parallel_executor(;max_threads::Int=0,
                           chunk_size::Int=100,
                           load_balancing::Bool=true,
                           progress_reporting::Bool=true,
                           memory_limit_per_thread::Float64=128.0)

創建平行回測執行器。

# Returns
- `ParallelBacktestExecutor`: 平行執行器
"""
function create_parallel_executor(;max_threads::Int=0,
                                 chunk_size::Int=100,
                                 load_balancing::Bool=true,
                                 progress_reporting::Bool=true,
                                 memory_limit_per_thread::Float64=128.0)::ParallelBacktestExecutor
    config = configure_parallel_execution(
        max_threads=max_threads,
        chunk_size=chunk_size,
        load_balancing=load_balancing,
        progress_reporting=progress_reporting,
        memory_limit_per_thread=memory_limit_per_thread
    )

    return ParallelBacktestExecutor(config)
end

"""
    get_execution_statistics(executor::ParallelBacktestExecutor)

獲取執行統計信息。

# Returns
- `NamedTuple`: 執行統計信息
"""
function get_execution_statistics(executor::ParallelBacktestExecutor)
    overall_progress = aggregate_progress(executor.progress_aggregator)
    active_tasks = executor.active_tasks[]

    return (
        total_progress = overall_progress,
        active_tasks = active_tasks,
        max_threads = executor.config.max_threads,
        chunk_size = executor.config.chunk_size,
        load_balancing = executor.config.load_balancing,
        progress_reporting = executor.config.progress_reporting
    )
end

end # module
