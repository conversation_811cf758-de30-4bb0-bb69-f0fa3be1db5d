
using Test
using Dates
using AmibrokerChartSimilator.BacktestingEngine
using AmibrokerChartSimilator.LotteryAnalysis

@testset "Reporting Tests" begin

    @testset "HTML Report Generation" begin
        # Create test data
        freq_criterion = create_frequency_criterion(0.1, 0.3, 50)
        strategy = SelectionStrategy("測試策略", "用於測試的策略", [freq_criterion], AND_LOGIC)

        # Create test executions
        executions = BacktestExecution[]
        for i in 1:10
            draw = LotteryDraw(i, Date(2023, 1, i), [1, 2, 3, 4, 5])
            selected = [6, 7, 8, 9, 10]
            confidence = Dict(6 => 0.8, 7 => 0.7, 8 => 0.6, 9 => 0.5, 10 => 0.4)
            exec = BacktestExecution(strategy, Date(2023, 1, i), selected, draw, confidence, 0.001)
            push!(executions, exec)
        end

        # Calculate results
        results = calculate_basic_metrics(executions)

        # Test HTML reporter
        reporter = HTMLReporter("professional", true, true)
        filename = "test_report.html"

        generated_file = generate_html_report(reporter, results, executions, filename)
        @test isfile(generated_file)
        @test generated_file == filename

        # Check file content
        content = read(filename, String)
        @test occursin("<!DOCTYPE html>", content)
        @test occursin("測試策略", content)
        @test occursin("回測報告", content)
        @test occursin("UTF-8", content)

        # Clean up
        rm(filename)
    end

    @testset "CSV Export Generation" begin
        # Create test data (same as above)
        freq_criterion = create_frequency_criterion(0.1, 0.3, 50)
        strategy = SelectionStrategy("測試策略", "用於測試的策略", [freq_criterion], AND_LOGIC)

        executions = BacktestExecution[]
        for i in 1:5
            draw = LotteryDraw(i, Date(2023, 1, i), [1, 2, 3, 4, 5])
            selected = [6, 7, 8, 9, 10]
            confidence = Dict(6 => 0.8, 7 => 0.7, 8 => 0.6, 9 => 0.5, 10 => 0.4)
            exec = BacktestExecution(strategy, Date(2023, 1, i), selected, draw, confidence, 0.001)
            push!(executions, exec)
        end

        results = calculate_basic_metrics(executions)

        # Test CSV exporter
        exporter = CSVExporter(",", true, "yyyy-mm-dd", "%.3f", "UTF-8")
        filename = "test_export.csv"

        generated_file = generate_csv_export(exporter, results, executions, filename)
        @test isfile(generated_file)
        @test generated_file == filename

        # Check file content
        content = read(filename, String)
        @test occursin("執行日期", content)
        @test occursin("選擇號碼", content)
        @test occursin("實際號碼", content)
        @test occursin("2023-01-01", content)

        # Clean up
        rm(filename)
    end

    @testset "Strategy Comparison Report" begin
        # Create test comparison data
        strategies = ["策略A", "策略B"]
        metrics = Dict{String, Dict{String, Float64}}(
            "策略A" => Dict("hit_rate" => 0.3, "roi" => 0.1, "avg_hits_per_execution" => 1.5),
            "策略B" => Dict("hit_rate" => 0.2, "roi" => 0.05, "avg_hits_per_execution" => 1.0)
        )
        tests = Dict{String, Float64}("test1" => 0.05)

        comparison = StrategyComparison(
            strategies, metrics, tests, "策略A", "策略A表現較佳"
        )

        # Test comparison report
        reporter = HTMLReporter("professional", true, true)
        filename = "test_comparison.html"

        generated_file = generate_comparison_report(reporter, comparison, filename)
        @test isfile(generated_file)
        @test generated_file == filename

        # Check file content
        content = read(filename, String)
        @test occursin("策略比較報告", content)
        @test occursin("策略A", content)
        @test occursin("策略B", content)
        @test occursin("最佳策略", content)

        # Clean up
        rm(filename)
    end

    @testset "HTMLReporter Configuration" begin
        # Test different configurations
        reporter1 = HTMLReporter("simple", false, false)
        @test reporter1.template_style == "simple"
        @test reporter1.include_charts == false
        @test reporter1.interactive_elements == false

        reporter2 = HTMLReporter("detailed", true, true, "body { color: red; }")
        @test reporter2.template_style == "detailed"
        @test reporter2.include_charts == true
        @test reporter2.interactive_elements == true
        @test reporter2.custom_css == "body { color: red; }"

        # Test invalid template style
        @test_throws ArgumentError HTMLReporter("invalid_style")
    end

    @testset "CSVExporter Configuration" begin
        # Test different configurations
        exporter1 = CSVExporter(";", false, "dd/mm/yyyy", "%.2f", "UTF-8")
        @test exporter1.delimiter == ";"
        @test exporter1.include_headers == false
        @test exporter1.date_format == "dd/mm/yyyy"
        @test exporter1.number_format == "%.2f"

        exporter2 = CSVExporter()  # Default values
        @test exporter2.delimiter == ","
        @test exporter2.include_headers == true
        @test exporter2.date_format == "yyyy-mm-dd"
        @test exporter2.number_format == "%.4f"
    end

end
