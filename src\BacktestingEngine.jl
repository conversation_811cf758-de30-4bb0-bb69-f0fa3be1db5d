module BacktestingEngine

using Dates, Statistics, Random
using SpecialFunctions: erf

# Include the LotteryAnalysis module
include("LotteryAnalysis.jl")
using .LotteryAnalysis: LotteryDraw

# Include the MemoryOptimization module
include("MemoryOptimization.jl")
using .MemoryOptimization: DataStreamer, MemoryMonitor, ChunkedDataProcessor
using .MemoryOptimization: create_data_streamer, process_chunked_data, monitor_memory_usage
using .MemoryOptimization: StreamingBacktestWindow, create_streaming_windows
using .MemoryOptimization: AutoMemoryOptimizer, auto_optimize
using .MemoryOptimization: has_more_data, read_next_chunk, reset_streamer

# Include the ParallelProcessing module
include("ParallelProcessing.jl")
using .ParallelProcessing: ParallelBacktestExecutor, ThreadSafeResults, ProgressAggregator
using .ParallelProcessing: execute_parallel_backtest, create_thread_safe_results
using .ParallelProcessing: ParallelConfiguration, configure_parallel_execution
using .ParallelProcessing: create_parallel_executor

# Note: Reporting module will be included separately to avoid circular dependencies

# ============================================================================
# Core Data Structures (Task 2.1)
# ============================================================================

@enum CombinationLogic begin
    AND_LOGIC = 1
    OR_LOGIC = 2
    WEIGHTED_LOGIC = 3
end

abstract type SelectionCriterion end

"""
    FrequencyCriterion

Criterion for selecting numbers based on their historical frequency of appearance.

# Fields
- `min_frequency::Float64`: Minimum frequency threshold (0.0 to 1.0)
- `max_frequency::Float64`: Maximum frequency threshold (0.0 to 1.0)
- `analysis_periods::Int`: Number of historical periods to analyze (must be positive)
"""
struct FrequencyCriterion <: SelectionCriterion
    min_frequency::Float64
    max_frequency::Float64
    analysis_periods::Int
    
    # Inner constructor with validation
    function FrequencyCriterion(min_freq::Float64, max_freq::Float64, periods::Int)
        if min_freq < 0.0 || min_freq > 1.0
            throw(ArgumentError("最小頻率必須在 0.0 到 1.0 之間: $min_freq"))
        end
        if max_freq < 0.0 || max_freq > 1.0
            throw(ArgumentError("最大頻率必須在 0.0 到 1.0 之間: $max_freq"))
        end
        if min_freq > max_freq
            throw(ArgumentError("最小頻率不能大於最大頻率: min=$min_freq, max=$max_freq"))
        end
        if periods <= 0
            throw(ArgumentError("分析期數必須為正數: $periods"))
        end
        if periods > 1000
            @warn "分析期數過大 ($periods)，可能影響性能"
        end
        new(min_freq, max_freq, periods)
    end
end

"""
    SkipCriterion

Criterion for selecting numbers based on their skip patterns (periods since last appearance).

# Fields
- `max_current_skip::Int`: Maximum allowed current skip value (must be non-negative)
- `skip_trend::String`: Expected skip trend ("ascending", "descending", "stable")
- `ffg_threshold::Float64`: FFG (Frequency of Future Gap) threshold (0.0 to 1.0)
"""
struct SkipCriterion <: SelectionCriterion
    max_current_skip::Int
    skip_trend::String
    ffg_threshold::Float64
    
    # Inner constructor with validation
    function SkipCriterion(max_skip::Int, trend::String, threshold::Float64)
        if max_skip < 0
            throw(ArgumentError("最大當前Skip值不能為負數: $max_skip"))
        end
        if max_skip > 100
            @warn "最大Skip值過大 ($max_skip)，可能過於嚴格"
        end
        
        valid_trends = ["ascending", "descending", "stable", "上升", "下降", "穩定"]
        if !(trend in valid_trends)
            throw(ArgumentError("無效的Skip趨勢: $trend. 必須是: $(join(valid_trends, ", "))"))
        end
        
        # Normalize Chinese trend names to English
        normalized_trend = trend
        if trend == "上升"
            normalized_trend = "ascending"
        elseif trend == "下降"
            normalized_trend = "descending"
        elseif trend == "穩定"
            normalized_trend = "stable"
        end
        
        if threshold < 0.0 || threshold > 1.0
            throw(ArgumentError("FFG閾值必須在 0.0 到 1.0 之間: $threshold"))
        end
        
        new(max_skip, normalized_trend, threshold)
    end
end

"""
    PairingCriterion

Criterion for selecting numbers based on their pairing relationships with target numbers.

# Fields
- `target_numbers::Vector{Int}`: Target numbers to analyze pairing with (1-39 range)
- `min_pairing_strength::Float64`: Minimum pairing strength threshold (0.0 to 1.0)
- `correlation_threshold::Float64`: Correlation threshold for pairing analysis (0.0 to 1.0)
"""
struct PairingCriterion <: SelectionCriterion
    target_numbers::Vector{Int}
    min_pairing_strength::Float64
    correlation_threshold::Float64
    
    # Inner constructor with validation
    function PairingCriterion(targets::Vector{Int}, min_strength::Float64, corr_threshold::Float64)
        if isempty(targets)
            throw(ArgumentError("目標號碼不能為空"))
        end
        if any(n -> n < 1 || n > 39, targets)
            throw(ArgumentError("目標號碼必須在 1 到 39 之間"))
        end
        if min_strength < 0.0 || min_strength > 1.0
            throw(ArgumentError("最小配對強度必須在 0.0 到 1.0 之間: $min_strength"))
        end
        if corr_threshold < 0.0 || corr_threshold > 1.0
            throw(ArgumentError("相關性閾值必須在 0.0 到 1.0 之間: $corr_threshold"))
        end
        if length(unique(targets)) != length(targets)
            @warn "目標號碼中有重複，將自動去重"
            targets = unique(targets)
        end
        new(targets, min_strength, corr_threshold)
    end
end

export SelectionCriterion, FrequencyCriterion, SkipCriterion, PairingCriterion
export CombinationLogic, AND_LOGIC, OR_LOGIC, WEIGHTED_LOGIC

# ============================================================================
# Constructor Functions with Validation (Task 2.1)
# ============================================================================

"""
    create_frequency_criterion(min_freq, max_freq, periods=100)

Create a FrequencyCriterion with validation.

# Arguments
- `min_freq::Float64`: Minimum frequency threshold (0.0 to 1.0)
- `max_freq::Float64`: Maximum frequency threshold (0.0 to 1.0)
- `periods::Int`: Number of historical periods to analyze (default: 100)

# Returns
- `FrequencyCriterion`: Validated frequency criterion

# Throws
- `ArgumentError`: If parameters are invalid
"""
function create_frequency_criterion(min_freq::Float64, max_freq::Float64, periods::Int=100)
    return FrequencyCriterion(min_freq, max_freq, periods)
end

"""
    create_skip_criterion(max_skip, trend="stable", threshold=0.5)

Create a SkipCriterion with validation.

# Arguments
- `max_skip::Int`: Maximum allowed current skip value
- `trend::String`: Expected skip trend ("ascending", "descending", "stable")
- `threshold::Float64`: FFG threshold (default: 0.5)

# Returns
- `SkipCriterion`: Validated skip criterion

# Throws
- `ArgumentError`: If parameters are invalid
"""
function create_skip_criterion(max_skip::Int, trend::String="stable", threshold::Float64=0.5)
    return SkipCriterion(max_skip, trend, threshold)
end

"""
    create_pairing_criterion(targets, min_strength=0.1, corr_threshold=0.3)

Create a PairingCriterion with validation.

# Arguments
- `targets::Vector{Int}`: Target numbers for pairing analysis (1-39 range)
- `min_strength::Float64`: Minimum pairing strength threshold (default: 0.1)
- `corr_threshold::Float64`: Correlation threshold (default: 0.3)

# Returns
- `PairingCriterion`: Validated pairing criterion

# Throws
- `ArgumentError`: If parameters are invalid
"""
function create_pairing_criterion(targets::Vector{Int}, min_strength::Float64=0.1, corr_threshold::Float64=0.3)
    return PairingCriterion(targets, min_strength, corr_threshold)
end



# ============================================================================
# Combination Logic Evaluation (Task 2.1)
# ============================================================================

function evaluate_combination_logic(logic::CombinationLogic, 
                                   criterion_results::Vector{Vector{Int}}, 
                                   weights::Vector{Float64}=Float64[])::Vector{Int}
    if isempty(criterion_results)
        return Int[]
    end
    
    if logic == AND_LOGIC
        # Intersection of all results
        result = criterion_results[1]
        for i in 2:length(criterion_results)
            result = intersect(result, criterion_results[i])
        end
        return sort(result)
        
    elseif logic == OR_LOGIC
        # Union of all results
        result = Set{Int}()
        for criterion_result in criterion_results
            union!(result, criterion_result)
        end
        return sort(collect(result))
        
    elseif logic == WEIGHTED_LOGIC
        # Weighted combination based on frequency of appearance
        if isempty(weights)
            weights = fill(1.0 / length(criterion_results), length(criterion_results))
        end
        
        # Count weighted appearances
        number_scores = Dict{Int, Float64}()
        for (i, criterion_result) in enumerate(criterion_results)
            weight = i <= length(weights) ? weights[i] : 1.0
            for num in criterion_result
                number_scores[num] = get(number_scores, num, 0.0) + weight
            end
        end
        
        # Sort by weighted score and return top candidates
        if isempty(number_scores)
            return Int[]
        end
        
        sorted_numbers = sort(collect(keys(number_scores)), by=x->number_scores[x], rev=true)
        
        # Return numbers with score above average
        avg_score = mean(values(number_scores))
        return filter(n -> number_scores[n] >= avg_score, sorted_numbers)
    else
        throw(ArgumentError("未知的組合邏輯: $logic"))
    end
end

# ============================================================================
# Criterion Logic Application (Task 2.1)
# ============================================================================

function apply_criterion_logic(criterion::SelectionCriterion, 
                              candidates::Vector{Int}, 
                              training_data::Vector{LotteryDraw})::Tuple{Vector{Int}, Dict{Int, Float64}}
    if isa(criterion, FrequencyCriterion)
        return apply_frequency_logic(criterion, candidates, training_data)
    elseif isa(criterion, SkipCriterion)
        return apply_skip_logic(criterion, candidates, training_data)
    elseif isa(criterion, PairingCriterion)
        return apply_pairing_logic(criterion, candidates, training_data)
    else
        return candidates, Dict{Int, Float64}()
    end
end

function apply_frequency_logic(criterion::FrequencyCriterion, 
                              candidates::Vector{Int}, 
                              training_data::Vector{LotteryDraw})::Tuple{Vector{Int}, Dict{Int, Float64}}
    analysis_data = training_data[max(1, end-criterion.analysis_periods+1):end]
    total_draws = length(analysis_data)
    
    selected = Int[]
    confidence_scores = Dict{Int, Float64}()
    
    for num in candidates
        frequency = count(draw -> num in draw.numbers, analysis_data) / total_draws
        
        if criterion.min_frequency <= frequency <= criterion.max_frequency
            push!(selected, num)
            # Confidence based on how close frequency is to the middle of the range
            range_middle = (criterion.min_frequency + criterion.max_frequency) / 2
            range_width = criterion.max_frequency - criterion.min_frequency
            if range_width > 0
                confidence = 1.0 - abs(frequency - range_middle) / (range_width / 2)
            else
                confidence = 1.0
            end
            confidence_scores[num] = max(0.0, min(1.0, confidence))
        end
    end
    
    return selected, confidence_scores
end

function apply_skip_logic(criterion::SkipCriterion, 
                         candidates::Vector{Int}, 
                         training_data::Vector{LotteryDraw})::Tuple{Vector{Int}, Dict{Int, Float64}}
    selected = Int[]
    confidence_scores = Dict{Int, Float64}()
    
    for num in candidates
        # Calculate current skip for this number
        current_skip = 0
        for i in length(training_data):-1:1
            if num in training_data[i].numbers
                break
            end
            current_skip += 1
        end
        
        if current_skip <= criterion.max_current_skip
            push!(selected, num)
            # Confidence based on skip value relative to threshold
            if criterion.max_current_skip > 0
                confidence = 1.0 - (current_skip / criterion.max_current_skip)
            else
                confidence = current_skip == 0 ? 1.0 : 0.0
            end
            confidence_scores[num] = max(0.0, min(1.0, confidence))
        end
    end
    
    return selected, confidence_scores
end

function apply_pairing_logic(criterion::PairingCriterion, 
                            candidates::Vector{Int}, 
                            training_data::Vector{LotteryDraw})::Tuple{Vector{Int}, Dict{Int, Float64}}
    selected = Int[]
    confidence_scores = Dict{Int, Float64}()
    
    # For each candidate, check pairing strength with target numbers
    for num in candidates
        if num in criterion.target_numbers
            continue  # Skip target numbers themselves
        end
        
        # Calculate pairing strength
        pairing_strength = 0.0
        total_target_appearances = 0
        
        for target in criterion.target_numbers
            target_appearances = count(draw -> target in draw.numbers, training_data)
            paired_appearances = count(draw -> target in draw.numbers && num in draw.numbers, training_data)
            
            if target_appearances > 0
                pairing_strength += paired_appearances / target_appearances
                total_target_appearances += target_appearances
            end
        end
        
        if total_target_appearances > 0
            avg_pairing_strength = pairing_strength / length(criterion.target_numbers)
            if avg_pairing_strength >= criterion.min_pairing_strength
                push!(selected, num)
                # Confidence based on pairing strength relative to threshold
                confidence = avg_pairing_strength / max(criterion.min_pairing_strength, 0.1)
                confidence_scores[num] = max(0.0, min(1.0, confidence))
            end
        end
    end
    
    return selected, confidence_scores
end

export create_frequency_criterion, create_skip_criterion, create_pairing_criterion
export evaluate_combination_logic, apply_criterion_logic

# ============================================================================
# Strategy Definition Structure (Task 2.2)
# ============================================================================

"""
    SelectionStrategy

Represents a complete strategy for number selection with multiple criteria.

# Fields
- `name::String`: Strategy name (must be non-empty)
- `description::String`: Strategy description
- `criteria::Vector{SelectionCriterion}`: List of selection criteria
- `combination_logic::CombinationLogic`: How to combine criteria results
- `parameters::Dict{String, Any}`: Additional strategy parameters
- `created_date::Date`: Strategy creation date
"""
struct SelectionStrategy
    name::String
    description::String
    criteria::Vector{SelectionCriterion}
    combination_logic::CombinationLogic
    parameters::Dict{String, Any}
    created_date::Date
    
    # Inner constructor with validation
    function SelectionStrategy(name::String, description::String, 
                              criteria::Vector{SelectionCriterion},
                              logic::CombinationLogic,
                              parameters::Dict{String, Any}=Dict{String, Any}(),
                              created_date::Date=today())
        if isempty(strip(name))
            throw(ArgumentError("策略名稱不能為空"))
        end
        if length(name) > 100
            throw(ArgumentError("策略名稱過長 (最多100字符): $(length(name))"))
        end
        if isempty(criteria)
            throw(ArgumentError("策略必須至少包含一個選擇條件"))
        end
        if length(criteria) > 10
            @warn "策略包含過多條件 ($(length(criteria)))，可能影響性能"
        end
        
        new(name, description, criteria, logic, parameters, created_date)
    end
end

export SelectionStrategy

# ============================================================================
# Data Processing and Windowing System (Task 3.1)
# ============================================================================

"""
    BacktestWindow

Represents a sliding window of historical data for backtesting.

# Fields
- `start_period::Int`: Starting period index in the dataset
- `end_period::Int`: Ending period index in the dataset
- `training_data::Vector{LotteryDraw}`: Historical data for strategy training
- `validation_data::Vector{LotteryDraw}`: Data for strategy validation/testing
"""
struct BacktestWindow
    start_period::Int
    end_period::Int
    training_data::Vector{LotteryDraw}
    validation_data::Vector{LotteryDraw}
    
    function BacktestWindow(start_period::Int, end_period::Int, 
                           training_data::Vector{LotteryDraw}, 
                           validation_data::Vector{LotteryDraw})
        if start_period < 1
            throw(ArgumentError("起始期數必須大於0: $start_period"))
        end
        if end_period < start_period
            throw(ArgumentError("結束期數必須大於等於起始期數: start=$start_period, end=$end_period"))
        end
        if isempty(training_data)
            throw(ArgumentError("訓練資料不能為空"))
        end
        if isempty(validation_data)
            throw(ArgumentError("驗證資料不能為空"))
        end
        
        new(start_period, end_period, training_data, validation_data)
    end
end

"""
    InsufficientDataError

Custom exception for insufficient historical data scenarios.
"""
struct InsufficientDataError <: Exception
    required_periods::Int
    available_periods::Int
    message::String
    
    function InsufficientDataError(required::Int, available::Int, msg::String="")
        if isempty(msg)
            msg = "資料不足：需要 $required 期，實際 $available 期"
        end
        new(required, available, msg)
    end
end

function Base.showerror(io::IO, e::InsufficientDataError)
    print(io, "InsufficientDataError: $(e.message)")
end

"""
    create_sliding_windows(data::Vector{LotteryDraw}, window_size::Int, step_size::Int=1, 
                          validation_size::Int=1)

Create sliding windows for backtesting from historical data.

# Arguments
- `data::Vector{LotteryDraw}`: Historical lottery data (chronologically ordered)
- `window_size::Int`: Size of training window (number of periods)
- `step_size::Int`: Step size between windows (default: 1)
- `validation_size::Int`: Size of validation window (default: 1)

# Returns
- `Vector{BacktestWindow}`: Array of sliding windows for backtesting

# Throws
- `ArgumentError`: If parameters are invalid
- `InsufficientDataError`: If insufficient data for requested windows
"""
function create_sliding_windows(data::Vector{LotteryDraw}, 
                               window_size::Int, 
                               step_size::Int=1,
                               validation_size::Int=1)::Vector{BacktestWindow}
    # Validate input parameters
    if window_size <= 0
        throw(ArgumentError("視窗大小必須為正數: $window_size"))
    end
    if step_size <= 0
        throw(ArgumentError("步長必須為正數: $step_size"))
    end
    if validation_size <= 0
        throw(ArgumentError("驗證資料大小必須為正數: $validation_size"))
    end
    if isempty(data)
        throw(ArgumentError("歷史資料不能為空"))
    end
    
    # Check if we have enough data
    min_required = window_size + validation_size
    if length(data) < min_required
        throw(InsufficientDataError(min_required, length(data)))
    end
    
    # Validate data integrity
    validate_historical_data(data)
    
    windows = BacktestWindow[]
    
    # Create sliding windows
    current_start = 1
    while current_start + window_size + validation_size - 1 <= length(data)
        training_end = current_start + window_size - 1
        validation_start = training_end + 1
        validation_end = validation_start + validation_size - 1
        
        # Extract training and validation data
        training_data = data[current_start:training_end]
        validation_data = data[validation_start:validation_end]
        
        # Create window
        window = BacktestWindow(current_start, validation_end, training_data, validation_data)
        push!(windows, window)
        
        # Move to next window
        current_start += step_size
    end
    
    if isempty(windows)
        throw(InsufficientDataError(min_required, length(data), 
                                   "無法創建任何視窗：資料長度 $(length(data))，需要至少 $min_required"))
    end
    
    @info "創建了 $(length(windows)) 個回測視窗，每個視窗包含 $window_size 期訓練資料和 $validation_size 期驗證資料"
    
    return windows
end

"""
    validate_historical_data(data::Vector{LotteryDraw})

Validate historical data for backtesting requirements.

# Arguments
- `data::Vector{LotteryDraw}`: Historical data to validate

# Throws
- `ArgumentError`: If data contains invalid entries
"""
function validate_historical_data(data::Vector{LotteryDraw})
    if isempty(data)
        throw(ArgumentError("歷史資料不能為空"))
    end
    
    # Check data format consistency
    for (i, draw) in enumerate(data)
        if length(draw.numbers) != 5
            throw(ArgumentError("第 $i 期資料格式錯誤：應包含5個號碼，實際 $(length(draw.numbers)) 個"))
        end
        
        if any(n -> n < 1 || n > 39, draw.numbers)
            invalid_numbers = filter(n -> n < 1 || n > 39, draw.numbers)
            throw(ArgumentError("第 $i 期包含無效號碼: $(invalid_numbers)，號碼應在1-39範圍內"))
        end
        
        if length(unique(draw.numbers)) != 5
            throw(ArgumentError("第 $i 期包含重複號碼: $(draw.numbers)"))
        end
    end
    
    # Check chronological order (if dates are available)
    dates_available = all(draw -> draw.draw_date != Date(1), data)
    if dates_available && length(data) > 1
        for i in 2:length(data)
            if data[i].draw_date < data[i-1].draw_date
                @warn "資料可能未按時間順序排列：第 $(i-1) 期 $(data[i-1].draw_date) 晚於第 $i 期 $(data[i].draw_date)"
            end
        end
    end
    
    # Check for data gaps (if draw_id is sequential)
    ids_available = all(draw -> draw.draw_id > 0, data)
    if ids_available && length(data) > 1
        expected_gaps = 0
        for i in 2:length(data)
            gap = data[i].draw_id - data[i-1].draw_id
            if gap != 1
                expected_gaps += 1
                if expected_gaps <= 3  # Only warn for first few gaps
                    @warn "資料可能有缺失：第 $(data[i-1].draw_id) 期到第 $(data[i].draw_id) 期之間缺少 $(gap-1) 期"
                end
            end
        end
        if expected_gaps > 3
            @warn "發現 $expected_gaps 個資料缺口，可能影響回測準確性"
        end
    end
end

"""
    preprocess_backtest_data(data::Vector{LotteryDraw}, 
                            remove_duplicates::Bool=true,
                            sort_by_date::Bool=true)

Preprocess historical data for backtesting.

# Arguments
- `data::Vector{LotteryDraw}`: Raw historical data
- `remove_duplicates::Bool`: Whether to remove duplicate draws (default: true)
- `sort_by_date::Bool`: Whether to sort by date (default: true)

# Returns
- `Vector{LotteryDraw}`: Preprocessed data ready for backtesting
"""
function preprocess_backtest_data(data::Vector{LotteryDraw}, 
                                 remove_duplicates::Bool=true,
                                 sort_by_date::Bool=true)::Vector{LotteryDraw}
    if isempty(data)
        return LotteryDraw[]
    end
    
    processed_data = copy(data)
    original_count = length(processed_data)
    
    # Remove duplicates if requested
    if remove_duplicates
        # Remove draws with identical numbers and dates
        unique_draws = Dict{Tuple{Vector{Int}, Date}, LotteryDraw}()
        for draw in processed_data
            key = (draw.numbers, draw.draw_date)
            if !haskey(unique_draws, key)
                unique_draws[key] = draw
            end
        end
        processed_data = collect(values(unique_draws))
        
        removed_count = original_count - length(processed_data)
        if removed_count > 0
            @info "移除了 $removed_count 個重複的開獎記錄"
        end
    end
    
    # Sort by date if requested
    if sort_by_date
        # Sort by date first, then by draw_id as secondary sort
        sort!(processed_data, by = draw -> (draw.draw_date, draw.draw_id))
        @info "資料已按日期排序"
    end
    
    # Final validation
    validate_historical_data(processed_data)
    
    @info "資料預處理完成：處理了 $original_count 期資料，最終 $(length(processed_data)) 期可用"
    
    return processed_data
end

"""
    get_window_statistics(windows::Vector{BacktestWindow})

Get statistics about the created windows.

# Arguments
- `windows::Vector{BacktestWindow}`: Array of backtest windows

# Returns
- `Dict{String, Any}`: Statistics about the windows
"""
function get_window_statistics(windows::Vector{BacktestWindow})::Dict{String, Any}
    if isempty(windows)
        return Dict{String, Any}(
            "total_windows" => 0,
            "avg_training_size" => 0,
            "avg_validation_size" => 0,
            "total_coverage" => 0
        )
    end
    
    training_sizes = [length(w.training_data) for w in windows]
    validation_sizes = [length(w.validation_data) for w in windows]
    
    # Calculate coverage (how much of the original data is used)
    min_start = minimum(w.start_period for w in windows)
    max_end = maximum(w.end_period for w in windows)
    total_coverage = max_end - min_start + 1
    
    return Dict{String, Any}(
        "total_windows" => length(windows),
        "avg_training_size" => mean(training_sizes),
        "min_training_size" => minimum(training_sizes),
        "max_training_size" => maximum(training_sizes),
        "avg_validation_size" => mean(validation_sizes),
        "min_validation_size" => minimum(validation_sizes),
        "max_validation_size" => maximum(validation_sizes),
        "total_coverage" => total_coverage,
        "coverage_efficiency" => length(windows) / total_coverage
    )
end

export BacktestWindow, InsufficientDataError
export create_sliding_windows, validate_historical_data, preprocess_backtest_data
export get_window_statistics

# ============================================================================
# Strategy Simulation Engine (Task 3.2)
# ============================================================================

"""
    BacktestExecution

Represents a single execution of a strategy against historical data.

# Fields
- `strategy::SelectionStrategy`: The strategy that was executed
- `execution_date::Date`: Date when the strategy was executed
- `selected_numbers::Vector{Int}`: Numbers selected by the strategy
- `actual_draw::LotteryDraw`: The actual lottery draw for comparison
- `hits::Int`: Number of correct predictions (0-5)
- `hit_positions::Vector{Int}`: Positions of the hits in the selected numbers
- `confidence_scores::Dict{Int, Float64}`: Confidence scores for each selected number
- `execution_time::Float64`: Time taken to execute the strategy (in seconds)
"""
struct BacktestExecution
    strategy::SelectionStrategy
    execution_date::Date
    selected_numbers::Vector{Int}
    actual_draw::LotteryDraw
    hits::Int
    hit_positions::Vector{Int}
    confidence_scores::Dict{Int, Float64}
    execution_time::Float64
    
    function BacktestExecution(strategy::SelectionStrategy, 
                              execution_date::Date,
                              selected_numbers::Vector{Int},
                              actual_draw::LotteryDraw,
                              confidence_scores::Dict{Int, Float64}=Dict{Int, Float64}(),
                              execution_time::Float64=0.0)
        if length(selected_numbers) != 5
            throw(ArgumentError("選擇的號碼必須是5個: $(length(selected_numbers))"))
        end
        if any(n -> n < 1 || n > 39, selected_numbers)
            throw(ArgumentError("選擇的號碼必須在1-39範圍內"))
        end
        if length(unique(selected_numbers)) != 5
            throw(ArgumentError("選擇的號碼不能重複"))
        end
        
        # Calculate hits
        hits = length(intersect(selected_numbers, actual_draw.numbers))
        
        # Find hit positions
        hit_positions = Int[]
        for (i, num) in enumerate(selected_numbers)
            if num in actual_draw.numbers
                push!(hit_positions, i)
            end
        end
        
        new(strategy, execution_date, sort(selected_numbers), actual_draw, 
            hits, hit_positions, confidence_scores, execution_time)
    end
end

"""
    SimulationProgress

Tracks progress during strategy simulation.
"""
mutable struct SimulationProgress
    total_executions::Int
    completed_executions::Int
    current_window::Int
    total_windows::Int
    start_time::Float64
    cancelled::Bool
    
    function SimulationProgress(total_executions::Int, total_windows::Int=1)
        new(total_executions, 0, 0, total_windows, time(), false)
    end
end

"""
    update_progress!(progress::SimulationProgress, completed::Int, current_window::Int=0)

Update simulation progress.
"""
function update_progress!(progress::SimulationProgress, completed::Int, current_window::Int=0)
    progress.completed_executions = completed
    if current_window > 0
        progress.current_window = current_window
    end
    
    # Print progress every 10% or every 100 executions
    if completed % max(1, progress.total_executions ÷ 10) == 0 || completed % 100 == 0
        elapsed = time() - progress.start_time
        rate = completed / elapsed
        remaining = (progress.total_executions - completed) / rate
        
        percentage = (completed / progress.total_executions) * 100
        @info "模擬進度: $(completed)/$(progress.total_executions) ($(round(percentage, digits=1))%), " *
              "預計剩餘時間: $(round(remaining, digits=1))秒"
    end
end

"""
    cancel_simulation!(progress::SimulationProgress)

Cancel the ongoing simulation.
"""
function cancel_simulation!(progress::SimulationProgress)
    progress.cancelled = true
    @info "模擬已取消"
end

"""
    simulate_strategy(strategy::SelectionStrategy, 
                     historical_data::Vector{LotteryDraw},
                     execution_period::Int)

Execute a strategy against a single historical data point.

# Arguments
- `strategy::SelectionStrategy`: Strategy to execute
- `historical_data::Vector{LotteryDraw}`: Historical data for strategy training
- `execution_period::Int`: Period index for the target draw

# Returns
- `BacktestExecution`: Execution result

# Throws
- `ArgumentError`: If parameters are invalid
- `InsufficientDataError`: If insufficient historical data
"""
function simulate_strategy(strategy::SelectionStrategy, 
                          historical_data::Vector{LotteryDraw},
                          execution_period::Int)::BacktestExecution
    if execution_period < 1 || execution_period > length(historical_data)
        throw(ArgumentError("執行期數超出範圍: $execution_period (可用範圍: 1-$(length(historical_data)))"))
    end
    
    start_time = time()
    
    # Get the target draw
    target_draw = historical_data[execution_period]
    
    # Use data before the execution period for training
    training_data = historical_data[1:execution_period-1]
    
    if isempty(training_data)
        throw(InsufficientDataError(1, 0, "執行期數 $execution_period 沒有足夠的歷史資料進行訓練"))
    end
    
    # Apply strategy to select numbers
    selected_numbers, confidence_scores = apply_strategy_selection(strategy, training_data)
    
    execution_time = time() - start_time
    
    return BacktestExecution(strategy, target_draw.draw_date, selected_numbers, 
                           target_draw, confidence_scores, execution_time)
end

"""
    simulate_strategy_batch(strategy::SelectionStrategy,
                           windows::Vector{BacktestWindow},
                           progress_callback::Union{Function, Nothing}=nothing)

Execute a strategy against multiple backtest windows.

# Arguments
- `strategy::SelectionStrategy`: Strategy to execute
- `windows::Vector{BacktestWindow}`: Backtest windows to process
- `progress_callback::Union{Function, Nothing}`: Optional progress callback function

# Returns
- `Vector{BacktestExecution}`: Array of execution results

# Throws
- `ArgumentError`: If parameters are invalid
"""
function simulate_strategy_batch(strategy::SelectionStrategy,
                                windows::Vector{BacktestWindow},
                                progress_callback::Union{Function, Nothing}=nothing)::Vector{BacktestExecution}
    if isempty(windows)
        throw(ArgumentError("回測視窗不能為空"))
    end
    
    # Validate strategy using the first window's training data as a sample
    if !isempty(windows)
        validate_strategy(strategy, windows[1].training_data)
    else
        validate_strategy(strategy)
    end
    
    executions = BacktestExecution[]
    total_validations = sum(length(w.validation_data) for w in windows)
    progress = SimulationProgress(total_validations, length(windows))
    
    @info "開始批次模擬：$(length(windows)) 個視窗，共 $total_validations 次執行"
    
    try
        for (window_idx, window) in enumerate(windows)
            if progress.cancelled
                @warn "模擬已取消，已完成 $(length(executions)) 次執行"
                break
            end
            
            # Process each validation period in the window
            for validation_draw in window.validation_data
                if progress.cancelled
                    break
                end
                
                start_time = time()
                
                # Apply strategy to select numbers using training data
                selected_numbers, confidence_scores = apply_strategy_selection(strategy, window.training_data)
                
                execution_time = time() - start_time
                
                # Create execution result
                execution = BacktestExecution(strategy, validation_draw.draw_date, 
                                            selected_numbers, validation_draw, 
                                            confidence_scores, execution_time)
                push!(executions, execution)
                
                # Update progress
                update_progress!(progress, length(executions), window_idx)
                
                # Call progress callback if provided
                if progress_callback !== nothing
                    progress_callback(progress, execution)
                end
            end
        end
        
        if !progress.cancelled
            elapsed_time = time() - progress.start_time
            @info "批次模擬完成：$(length(executions)) 次執行，耗時 $(round(elapsed_time, digits=2)) 秒"
        end
        
    catch e
        @error "模擬過程中發生錯誤: $e"
        rethrow(e)
    end
    
    return executions
end

"""
    apply_strategy_selection(strategy::SelectionStrategy, training_data::Vector{LotteryDraw})

Apply a strategy to select numbers based on training data.

# Arguments
- `strategy::SelectionStrategy`: Strategy to apply
- `training_data::Vector{LotteryDraw}`: Historical data for analysis

# Returns
- `Tuple{Vector{Int}, Dict{Int, Float64}}`: Selected numbers and confidence scores

# Throws
- `ArgumentError`: If strategy cannot be applied
"""
function apply_strategy_selection(strategy::SelectionStrategy, 
                                 training_data::Vector{LotteryDraw})::Tuple{Vector{Int}, Dict{Int, Float64}}
    if isempty(training_data)
        throw(ArgumentError("訓練資料不能為空"))
    end
    
    # Get all possible candidates (1-39)
    all_candidates = collect(1:39)
    
    # Apply each criterion to get candidate lists
    criterion_results = Vector{Int}[]
    all_confidence_scores = Dict{Int, Float64}()
    
    for criterion in strategy.criteria
        selected_candidates, confidence_scores = apply_criterion_logic(criterion, all_candidates, training_data)
        push!(criterion_results, selected_candidates)
        
        # Merge confidence scores
        for (num, score) in confidence_scores
            if haskey(all_confidence_scores, num)
                # Average the confidence scores from different criteria
                all_confidence_scores[num] = (all_confidence_scores[num] + score) / 2
            else
                all_confidence_scores[num] = score
            end
        end
    end
    
    # Combine results using the strategy's combination logic
    combined_candidates = evaluate_combination_logic(strategy.combination_logic, criterion_results)
    
    # If we have more than 5 candidates, select top 5 based on confidence scores
    if length(combined_candidates) > 5
        # Sort by confidence score (descending) and take top 5
        sorted_candidates = sort(combined_candidates, 
                               by=x->get(all_confidence_scores, x, 0.0), 
                               rev=true)
        selected_numbers = sorted_candidates[1:5]
    elseif length(combined_candidates) == 5
        selected_numbers = combined_candidates
    else
        # If we have fewer than 5 candidates, we need to add more
        # Add highest confidence numbers that weren't already selected
        remaining_candidates = setdiff(all_candidates, combined_candidates)
        
        if !isempty(remaining_candidates)
            # Sort remaining by confidence score
            sorted_remaining = sort(remaining_candidates,
                                  by=x->get(all_confidence_scores, x, 0.0),
                                  rev=true)
            
            # Add candidates until we have 5
            needed = 5 - length(combined_candidates)
            additional = sorted_remaining[1:min(needed, length(sorted_remaining))]
            selected_numbers = vcat(combined_candidates, additional)
        else
            selected_numbers = combined_candidates
        end
        
        # If still not enough, fill with random numbers (fallback)
        if length(selected_numbers) < 5
            remaining_numbers = setdiff(all_candidates, selected_numbers)
            needed = 5 - length(selected_numbers)
            additional = shuffle(remaining_numbers)[1:needed]
            selected_numbers = vcat(selected_numbers, additional)
        end
    end
    
    # Ensure we have exactly 5 unique numbers
    selected_numbers = unique(selected_numbers)[1:5]
    
    # Filter confidence scores for selected numbers
    selected_confidence_scores = Dict{Int, Float64}()
    for num in selected_numbers
        selected_confidence_scores[num] = get(all_confidence_scores, num, 0.5)  # Default confidence
    end
    
    return sort(selected_numbers), selected_confidence_scores
end

"""
    get_execution_statistics(executions::Vector{BacktestExecution})

Calculate statistics from a collection of backtest executions.

# Arguments
- `executions::Vector{BacktestExecution}`: Execution results to analyze

# Returns
- `Dict{String, Any}`: Statistics about the executions
"""
function get_execution_statistics(executions::Vector{BacktestExecution})::Dict{String, Any}
    if isempty(executions)
        return Dict{String, Any}(
            "total_executions" => 0,
            "total_hits" => 0,
            "hit_rate" => 0.0,
            "avg_hits_per_execution" => 0.0,
            "max_hits" => 0,
            "min_hits" => 0,
            "hit_distribution" => Dict{Int, Int}(),
            "avg_execution_time" => 0.0,
            "total_execution_time" => 0.0
        )
    end
    
    total_executions = length(executions)
    total_hits = sum(e.hits for e in executions)
    hit_rates = [e.hits for e in executions]
    execution_times = [e.execution_time for e in executions]
    
    # Calculate hit distribution
    hit_distribution = Dict{Int, Int}()
    for hits in 0:5
        hit_distribution[hits] = count(e -> e.hits == hits, executions)
    end
    
    return Dict{String, Any}(
        "total_executions" => total_executions,
        "total_hits" => total_hits,
        "hit_rate" => total_hits / (total_executions * 5),  # Percentage of all possible hits
        "avg_hits_per_execution" => mean(hit_rates),
        "max_hits" => maximum(hit_rates),
        "min_hits" => minimum(hit_rates),
        "hit_distribution" => hit_distribution,
        "avg_execution_time" => mean(execution_times),
        "total_execution_time" => sum(execution_times),
        "std_hits" => std(hit_rates),
        "median_hits" => median(hit_rates)
    )
end

export BacktestExecution, SimulationProgress
export simulate_strategy, simulate_strategy_batch, apply_strategy_selection
export update_progress!, cancel_simulation!, get_execution_statistics

# ============================================================================
# Performance Metrics Calculation System (Task 4.1)
# ============================================================================

"""
    BacktestResults

Comprehensive results from a backtest execution with performance metrics.

# Fields
- `strategy_name::String`: Name of the tested strategy
- `total_executions::Int`: Total number of strategy executions
- `total_hits::Int`: Total number of correct predictions across all executions
- `hit_rate::Float64`: Overall hit rate (percentage of correct predictions)
- `roi::Float64`: Return on investment assuming standard lottery payouts
- `max_consecutive_misses::Int`: Maximum consecutive periods without any hits
- `max_consecutive_hits::Int`: Maximum consecutive periods with at least one hit
- `average_hits_per_execution::Float64`: Average number of hits per execution
- `hit_distribution::Dict{Int, Int}`: Distribution of hits (0-5 hits per execution)
- `execution_period::Tuple{Date, Date}`: Start and end dates of the backtest period
- `total_investment::Float64`: Total amount invested (assuming unit cost per execution)
- `total_return::Float64`: Total return from winnings
- `best_execution::Union{BacktestExecution, Nothing}`: Execution with most hits
- `worst_streak_start::Int`: Starting execution index of worst miss streak
- `best_streak_start::Int`: Starting execution index of best hit streak
"""
struct BacktestResults
    strategy_name::String
    total_executions::Int
    total_hits::Int
    hit_rate::Float64
    roi::Float64
    max_consecutive_misses::Int
    max_consecutive_hits::Int
    average_hits_per_execution::Float64
    hit_distribution::Dict{Int, Int}
    execution_period::Tuple{Date, Date}
    total_investment::Float64
    total_return::Float64
    best_execution::Union{BacktestExecution, Nothing}
    worst_streak_start::Int
    best_streak_start::Int
    
    function BacktestResults(strategy_name::String, total_executions::Int, total_hits::Int,
                            hit_rate::Float64, roi::Float64, max_consecutive_misses::Int,
                            max_consecutive_hits::Int, average_hits_per_execution::Float64,
                            hit_distribution::Dict{Int, Int}, execution_period::Tuple{Date, Date},
                            total_investment::Float64, total_return::Float64,
                            best_execution::Union{BacktestExecution, Nothing},
                            worst_streak_start::Int, best_streak_start::Int)
        if total_executions < 0
            throw(ArgumentError("總執行次數不能為負數: $total_executions"))
        end
        if total_hits < 0
            throw(ArgumentError("總命中數不能為負數: $total_hits"))
        end
        if hit_rate < 0.0 || hit_rate > 1.0
            throw(ArgumentError("命中率必須在0.0到1.0之間: $hit_rate"))
        end
        if max_consecutive_misses < 0
            throw(ArgumentError("最大連續未中次數不能為負數: $max_consecutive_misses"))
        end
        if max_consecutive_hits < 0
            throw(ArgumentError("最大連續命中次數不能為負數: $max_consecutive_hits"))
        end
        
        new(strategy_name, total_executions, total_hits, hit_rate, roi,
            max_consecutive_misses, max_consecutive_hits, average_hits_per_execution,
            hit_distribution, execution_period, total_investment, total_return,
            best_execution, worst_streak_start, best_streak_start)
    end
end

"""
    calculate_basic_metrics(executions::Vector{BacktestExecution})

Calculate basic performance metrics from backtest executions.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `BacktestResults`: Comprehensive performance metrics

# Throws
- `ArgumentError`: If executions array is empty or contains invalid data
"""
function calculate_basic_metrics(executions::Vector{BacktestExecution})::BacktestResults
    if isempty(executions)
        throw(ArgumentError("執行結果不能為空"))
    end
    
    # Validate all executions are from the same strategy
    strategy_name = executions[1].strategy.name
    if !all(exec -> exec.strategy.name == strategy_name, executions)
        throw(ArgumentError("所有執行結果必須來自同一策略"))
    end
    
    # Basic counts
    total_executions = length(executions)
    total_hits = sum(exec -> exec.hits, executions)
    
    # Hit rate calculation
    hit_rate = total_hits / (total_executions * 5)  # 5 numbers per execution
    average_hits_per_execution = total_hits / total_executions
    
    # Hit distribution
    hit_distribution = Dict{Int, Int}()
    for i in 0:5
        hit_distribution[i] = count(exec -> exec.hits == i, executions)
    end
    
    # Consecutive streaks calculation
    max_consecutive_misses, worst_streak_start = calculate_max_consecutive_misses(executions)
    max_consecutive_hits, best_streak_start = calculate_max_consecutive_hits(executions)
    
    # Financial calculations (assuming standard lottery payouts)
    total_investment = Float64(total_executions)  # Assuming 1 unit cost per execution
    total_return = calculate_total_return(executions)
    roi = total_executions > 0 ? (total_return - total_investment) / total_investment : 0.0
    
    # Execution period
    execution_dates = [exec.execution_date for exec in executions]
    execution_period = (minimum(execution_dates), maximum(execution_dates))
    
    # Best execution (most hits)
    best_execution = find_best_execution(executions)
    
    return BacktestResults(
        strategy_name, total_executions, total_hits, hit_rate, roi,
        max_consecutive_misses, max_consecutive_hits, average_hits_per_execution,
        hit_distribution, execution_period, total_investment, total_return,
        best_execution, worst_streak_start, best_streak_start
    )
end

"""
    calculate_max_consecutive_misses(executions::Vector{BacktestExecution})

Calculate the maximum consecutive executions without any hits.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Tuple{Int, Int}`: (max_consecutive_misses, streak_start_index)
"""
function calculate_max_consecutive_misses(executions::Vector{BacktestExecution})::Tuple{Int, Int}
    if isempty(executions)
        return (0, 0)
    end
    
    max_streak = 0
    current_streak = 0
    max_streak_start = 0
    current_streak_start = 0
    
    for (i, exec) in enumerate(executions)
        if exec.hits == 0
            if current_streak == 0
                current_streak_start = i
            end
            current_streak += 1
        else
            if current_streak > max_streak
                max_streak = current_streak
                max_streak_start = current_streak_start
            end
            current_streak = 0
        end
    end
    
    # Check if the last streak is the longest
    if current_streak > max_streak
        max_streak = current_streak
        max_streak_start = current_streak_start
    end
    
    return (max_streak, max_streak_start)
end

"""
    calculate_max_consecutive_hits(executions::Vector{BacktestExecution})

Calculate the maximum consecutive executions with at least one hit.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Tuple{Int, Int}`: (max_consecutive_hits, streak_start_index)
"""
function calculate_max_consecutive_hits(executions::Vector{BacktestExecution})::Tuple{Int, Int}
    if isempty(executions)
        return (0, 0)
    end
    
    max_streak = 0
    current_streak = 0
    max_streak_start = 0
    current_streak_start = 0
    
    for (i, exec) in enumerate(executions)
        if exec.hits > 0
            if current_streak == 0
                current_streak_start = i
            end
            current_streak += 1
        else
            if current_streak > max_streak
                max_streak = current_streak
                max_streak_start = current_streak_start
            end
            current_streak = 0
        end
    end
    
    # Check if the last streak is the longest
    if current_streak > max_streak
        max_streak = current_streak
        max_streak_start = current_streak_start
    end
    
    return (max_streak, max_streak_start)
end

"""
    calculate_total_return(executions::Vector{BacktestExecution})

Calculate total return based on standard lottery payout structure.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Float64`: Total return from winnings

# Note
Payout structure (assuming standard Pick 5 from 39 lottery):
- 5 hits: 100,000 units (jackpot)
- 4 hits: 1,000 units
- 3 hits: 100 units
- 2 hits: 10 units
- 1 hit: 2 units
- 0 hits: 0 units
"""
function calculate_total_return(executions::Vector{BacktestExecution})::Float64
    # Standard payout structure for Pick 5 from 39
    payout_table = Dict{Int, Float64}(
        0 => 0.0,
        1 => 2.0,
        2 => 10.0,
        3 => 100.0,
        4 => 1000.0,
        5 => 100000.0
    )
    
    total_return = 0.0
    for exec in executions
        total_return += get(payout_table, exec.hits, 0.0)
    end
    
    return total_return
end

"""
    find_best_execution(executions::Vector{BacktestExecution})

Find the execution with the most hits (best performance).

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Union{BacktestExecution, Nothing}`: Best execution or nothing if empty
"""
function find_best_execution(executions::Vector{BacktestExecution})::Union{BacktestExecution, Nothing}
    if isempty(executions)
        return nothing
    end
    
    best_exec = executions[1]
    for exec in executions[2:end]
        if exec.hits > best_exec.hits
            best_exec = exec
        elseif exec.hits == best_exec.hits
            # If same number of hits, prefer the one with higher confidence
            if !isempty(exec.confidence_scores) && !isempty(best_exec.confidence_scores)
                exec_avg_confidence = mean(values(exec.confidence_scores))
                best_avg_confidence = mean(values(best_exec.confidence_scores))
                if exec_avg_confidence > best_avg_confidence
                    best_exec = exec
                end
            end
        end
    end
    
    return best_exec
end

"""
    calculate_hit_rate_by_position(executions::Vector{BacktestExecution})

Calculate hit rate for each position in the selected numbers.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Vector{Float64}`: Hit rate for each position (1-5)
"""
function calculate_hit_rate_by_position(executions::Vector{BacktestExecution})::Vector{Float64}
    if isempty(executions)
        return Float64[]
    end
    
    position_hits = zeros(Int, 5)
    total_executions = length(executions)
    
    for exec in executions
        for pos in exec.hit_positions
            if 1 <= pos <= 5
                position_hits[pos] += 1
            end
        end
    end
    
    return position_hits ./ total_executions
end

"""
    calculate_number_frequency_performance(executions::Vector{BacktestExecution})

Calculate performance metrics for each number (1-39).

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Dict{Int, Dict{String, Float64}}`: Performance metrics for each number
"""
function calculate_number_frequency_performance(executions::Vector{BacktestExecution})::Dict{Int, Dict{String, Float64}}
    number_stats = Dict{Int, Dict{String, Float64}}()
    
    # Initialize stats for all numbers 1-39
    for num in 1:39
        number_stats[num] = Dict{String, Float64}(
            "selected_count" => 0.0,
            "hit_count" => 0.0,
            "hit_rate" => 0.0,
            "avg_confidence" => 0.0
        )
    end
    
    # Calculate statistics
    for exec in executions
        for (i, num) in enumerate(exec.selected_numbers)
            number_stats[num]["selected_count"] += 1.0
            
            if num in exec.actual_draw.numbers
                number_stats[num]["hit_count"] += 1.0
            end
            
            # Add confidence score if available
            if haskey(exec.confidence_scores, num)
                current_avg = number_stats[num]["avg_confidence"]
                current_count = number_stats[num]["selected_count"]
                new_confidence = exec.confidence_scores[num]
                number_stats[num]["avg_confidence"] = 
                    (current_avg * (current_count - 1) + new_confidence) / current_count
            end
        end
    end
    
    # Calculate hit rates
    for num in 1:39
        selected_count = number_stats[num]["selected_count"]
        if selected_count > 0
            number_stats[num]["hit_rate"] = number_stats[num]["hit_count"] / selected_count
        end
    end
    
    return number_stats
end

export BacktestResults, calculate_basic_metrics
export calculate_max_consecutive_misses, calculate_max_consecutive_hits
export calculate_total_return, find_best_execution
export calculate_hit_rate_by_position, calculate_number_frequency_performance

# ============================================================================
# Statistical Significance and Confidence Intervals (Task 4.2)
# ============================================================================

"""
    calculate_chi_square_test(executions::Vector{BacktestExecution})

Perform chi-square test for randomness assessment of hit distribution.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Tuple{Float64, Float64}`: (chi_square_statistic, p_value)

# Note
Tests whether the observed hit distribution significantly differs from expected random distribution.
"""
function calculate_chi_square_test(executions::Vector{BacktestExecution})::Tuple{Float64, Float64}
    if isempty(executions)
        return (0.0, 1.0)
    end
    
    # Calculate observed frequencies for each hit count (0-5)
    observed = zeros(Int, 6)  # Index 1 = 0 hits, Index 2 = 1 hit, etc.
    for exec in executions
        observed[exec.hits + 1] += 1
    end
    
    total_executions = length(executions)
    
    # Calculate expected frequencies based on hypergeometric distribution
    # For Pick 5 from 39 lottery, probability of exactly k hits when selecting 5 numbers
    expected = zeros(Float64, 6)
    
    # Hypergeometric probabilities for Pick 5 from 39
    # P(k hits) = C(5,k) * C(34,5-k) / C(39,5)
    total_combinations = binomial(39, 5)  # Total ways to choose 5 from 39
    
    for k in 0:5
        if k <= 5 && (5-k) <= 34
            prob = (binomial(5, k) * binomial(34, 5-k)) / total_combinations
            expected[k + 1] = prob * total_executions
        end
    end
    
    # Calculate chi-square statistic
    chi_square = 0.0
    degrees_of_freedom = 0
    
    for i in 1:6
        if expected[i] >= 5.0  # Only include categories with expected frequency >= 5
            chi_square += (observed[i] - expected[i])^2 / expected[i]
            degrees_of_freedom += 1
        end
    end
    
    degrees_of_freedom = max(1, degrees_of_freedom - 1)  # df = categories - 1
    
    # Calculate p-value using chi-square distribution approximation
    # For simplicity, using a basic approximation
    p_value = calculate_chi_square_p_value(chi_square, degrees_of_freedom)
    
    return (chi_square, p_value)
end

"""
    calculate_chi_square_p_value(chi_square::Float64, df::Int)

Calculate p-value for chi-square test using approximation.

# Arguments
- `chi_square::Float64`: Chi-square test statistic
- `df::Int`: Degrees of freedom

# Returns
- `Float64`: Approximate p-value
"""
function calculate_chi_square_p_value(chi_square::Float64, df::Int)::Float64
    if chi_square <= 0.0 || df <= 0
        return 1.0
    end
    
    # Simple approximation for chi-square p-value
    # Using Wilson-Hilferty approximation for large df
    if df >= 30
        h = 2.0 / (9.0 * df)
        z = (((chi_square / df)^(1/3) - (1 - h)) / sqrt(h))
        # Approximate standard normal CDF
        p_value = 0.5 * (1 + erf(-z / sqrt(2)))
        return max(0.0, min(1.0, p_value))
    end
    
    # For smaller df, use lookup table approximation
    critical_values = Dict{Int, Vector{Float64}}(
        1 => [3.84, 6.64, 10.83],      # p = 0.05, 0.01, 0.001
        2 => [5.99, 9.21, 13.82],
        3 => [7.81, 11.34, 16.27],
        4 => [9.49, 13.28, 18.47],
        5 => [11.07, 15.09, 20.52]
    )
    
    if haskey(critical_values, df)
        values = critical_values[df]
        if chi_square < values[1]
            return 0.5  # p > 0.05
        elseif chi_square < values[2]
            return 0.025  # 0.01 < p < 0.05
        elseif chi_square < values[3]
            return 0.005  # 0.001 < p < 0.01
        else
            return 0.0005  # p < 0.001
        end
    end
    
    # Default approximation for other df values
    if chi_square > df + 2 * sqrt(2 * df)
        return 0.05
    else
        return 0.5
    end
end

"""
    calculate_bootstrap_confidence_interval(executions::Vector{BacktestExecution}, 
                                          metric_func::Function, 
                                          confidence_level::Float64=0.95,
                                          n_bootstrap::Int=1000)

Calculate bootstrap confidence interval for a given metric.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions
- `metric_func::Function`: Function to calculate the metric (takes executions, returns Float64)
- `confidence_level::Float64`: Confidence level (default: 0.95)
- `n_bootstrap::Int`: Number of bootstrap samples (default: 1000)

# Returns
- `Tuple{Float64, Float64}`: (lower_bound, upper_bound) of confidence interval
"""
function calculate_bootstrap_confidence_interval(executions::Vector{BacktestExecution}, 
                                               metric_func::Function, 
                                               confidence_level::Float64=0.95,
                                               n_bootstrap::Int=1000)::Tuple{Float64, Float64}
    if isempty(executions)
        return (0.0, 0.0)
    end
    
    if confidence_level <= 0.0 || confidence_level >= 1.0
        throw(ArgumentError("信心水準必須在 0 到 1 之間: $confidence_level"))
    end
    
    if n_bootstrap <= 0
        throw(ArgumentError("Bootstrap 樣本數必須為正數: $n_bootstrap"))
    end
    
    n_executions = length(executions)
    bootstrap_values = Float64[]
    
    # Generate bootstrap samples
    for _ in 1:n_bootstrap
        # Sample with replacement
        bootstrap_sample = [executions[rand(1:n_executions)] for _ in 1:n_executions]
        
        try
            metric_value = metric_func(bootstrap_sample)
            push!(bootstrap_values, metric_value)
        catch e
            # Skip invalid bootstrap samples
            continue
        end
    end
    
    if isempty(bootstrap_values)
        return (0.0, 0.0)
    end
    
    # Calculate confidence interval
    sort!(bootstrap_values)
    alpha = 1.0 - confidence_level
    lower_percentile = alpha / 2.0
    upper_percentile = 1.0 - alpha / 2.0
    
    n_values = length(bootstrap_values)
    lower_index = max(1, Int(ceil(lower_percentile * n_values)))
    upper_index = min(n_values, Int(floor(upper_percentile * n_values)))
    
    return (bootstrap_values[lower_index], bootstrap_values[upper_index])
end

"""
    calculate_statistical_significance(executions::Vector{BacktestExecution})

Calculate statistical significance of strategy performance compared to random selection.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions

# Returns
- `Dict{String, Float64}`: Dictionary containing various significance measures
"""
function calculate_statistical_significance(executions::Vector{BacktestExecution})::Dict{String, Float64}
    if isempty(executions)
        return Dict{String, Float64}(
            "chi_square_statistic" => 0.0,
            "chi_square_p_value" => 1.0,
            "hit_rate_z_score" => 0.0,
            "hit_rate_p_value" => 1.0,
            "is_significant_05" => 0.0,
            "is_significant_01" => 0.0
        )
    end
    
    # Chi-square test for distribution randomness
    chi_square_stat, chi_square_p = calculate_chi_square_test(executions)
    
    # Z-test for hit rate compared to expected random hit rate
    total_hits = sum(exec -> exec.hits, executions)
    total_possible = length(executions) * 5
    observed_hit_rate = total_hits / total_possible
    
    # Expected hit rate for random selection in Pick 5 from 39
    # Each number has 5/39 probability of being selected
    expected_hit_rate = 5.0 / 39.0
    
    # Standard error for hit rate
    n = total_possible
    se = sqrt(expected_hit_rate * (1 - expected_hit_rate) / n)
    
    # Z-score
    z_score = se > 0 ? (observed_hit_rate - expected_hit_rate) / se : 0.0
    
    # P-value for two-tailed test
    hit_rate_p_value = 2.0 * (1.0 - standard_normal_cdf(abs(z_score)))
    
    return Dict{String, Float64}(
        "chi_square_statistic" => chi_square_stat,
        "chi_square_p_value" => chi_square_p,
        "hit_rate_z_score" => z_score,
        "hit_rate_p_value" => hit_rate_p_value,
        "observed_hit_rate" => observed_hit_rate,
        "expected_hit_rate" => expected_hit_rate,
        "is_significant_05" => (hit_rate_p_value < 0.05) ? 1.0 : 0.0,
        "is_significant_01" => (hit_rate_p_value < 0.01) ? 1.0 : 0.0
    )
end

"""
    standard_normal_cdf(z::Float64)

Calculate cumulative distribution function of standard normal distribution.

# Arguments
- `z::Float64`: Z-score

# Returns
- `Float64`: Cumulative probability
"""
function standard_normal_cdf(z::Float64)::Float64
    # Approximation using error function
    return 0.5 * (1.0 + erf(z / sqrt(2.0)))
end

"""
    calculate_performance_trend(executions::Vector{BacktestExecution}, window_size::Int=20)

Analyze performance trends over time periods.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions (should be chronologically ordered)
- `window_size::Int`: Size of rolling window for trend analysis (default: 20)

# Returns
- `Dict{String, Any}`: Trend analysis results
"""
function calculate_performance_trend(executions::Vector{BacktestExecution}, window_size::Int=20)::Dict{String, Any}
    if length(executions) < window_size
        return Dict{String, Any}(
            "trend_slope" => 0.0,
            "trend_r_squared" => 0.0,
            "rolling_hit_rates" => Float64[],
            "trend_direction" => "insufficient_data",
            "volatility" => 0.0
        )
    end
    
    # Calculate rolling hit rates
    rolling_hit_rates = Float64[]
    for i in window_size:length(executions)
        window_executions = executions[(i-window_size+1):i]
        window_hits = sum(exec -> exec.hits, window_executions)
        window_hit_rate = window_hits / (window_size * 5)
        push!(rolling_hit_rates, window_hit_rate)
    end
    
    if length(rolling_hit_rates) < 2
        return Dict{String, Any}(
            "trend_slope" => 0.0,
            "trend_r_squared" => 0.0,
            "rolling_hit_rates" => rolling_hit_rates,
            "trend_direction" => "insufficient_data",
            "volatility" => 0.0
        )
    end
    
    # Linear regression for trend analysis
    n = length(rolling_hit_rates)
    x_values = collect(1:n)
    y_values = rolling_hit_rates
    
    # Calculate slope and R-squared
    x_mean = mean(x_values)
    y_mean = mean(y_values)
    
    numerator = sum((x_values[i] - x_mean) * (y_values[i] - y_mean) for i in 1:n)
    denominator = sum((x_values[i] - x_mean)^2 for i in 1:n)
    
    slope = denominator > 0 ? numerator / denominator : 0.0
    
    # R-squared calculation
    y_pred = [y_mean + slope * (x - x_mean) for x in x_values]
    ss_res = sum((y_values[i] - y_pred[i])^2 for i in 1:n)
    ss_tot = sum((y_values[i] - y_mean)^2 for i in 1:n)
    r_squared = ss_tot > 0 ? 1.0 - (ss_res / ss_tot) : 0.0
    
    # Determine trend direction
    trend_direction = if abs(slope) < 0.001
        "stable"
    elseif slope > 0
        "improving"
    else
        "declining"
    end
    
    # Calculate volatility (standard deviation of rolling hit rates)
    volatility = std(rolling_hit_rates)
    
    return Dict{String, Any}(
        "trend_slope" => slope,
        "trend_r_squared" => r_squared,
        "rolling_hit_rates" => rolling_hit_rates,
        "trend_direction" => trend_direction,
        "volatility" => volatility,
        "window_size" => window_size
    )
end

"""
    enhanced_backtest_results(executions::Vector{BacktestExecution}, 
                             confidence_level::Float64=0.95,
                             n_bootstrap::Int=1000)

Calculate enhanced backtest results with statistical significance and confidence intervals.

# Arguments
- `executions::Vector{BacktestExecution}`: Array of backtest executions
- `confidence_level::Float64`: Confidence level for intervals (default: 0.95)
- `n_bootstrap::Int`: Number of bootstrap samples (default: 1000)

# Returns
- `Dict{String, Any}`: Enhanced results with statistical measures
"""
function enhanced_backtest_results(executions::Vector{BacktestExecution}, 
                                 confidence_level::Float64=0.95,
                                 n_bootstrap::Int=1000)::Dict{String, Any}
    if isempty(executions)
        return Dict{String, Any}()
    end
    
    # Basic metrics
    basic_results = calculate_basic_metrics(executions)
    
    # Statistical significance
    significance = calculate_statistical_significance(executions)
    
    # Bootstrap confidence intervals for key metrics
    hit_rate_ci = calculate_bootstrap_confidence_interval(
        executions, 
        execs -> sum(e -> e.hits, execs) / (length(execs) * 5),
        confidence_level, 
        n_bootstrap
    )
    
    roi_ci = calculate_bootstrap_confidence_interval(
        executions,
        execs -> begin
            total_return = calculate_total_return(execs)
            total_investment = Float64(length(execs))
            return total_investment > 0 ? (total_return - total_investment) / total_investment : 0.0
        end,
        confidence_level,
        n_bootstrap
    )
    
    # Performance trend analysis
    trend_analysis = calculate_performance_trend(executions)
    
    return Dict{String, Any}(
        "basic_results" => basic_results,
        "statistical_significance" => significance,
        "confidence_intervals" => Dict{String, Any}(
            "hit_rate" => hit_rate_ci,
            "roi" => roi_ci,
            "confidence_level" => confidence_level
        ),
        "trend_analysis" => trend_analysis,
        "bootstrap_samples" => n_bootstrap
    )
end

export calculate_chi_square_test, calculate_bootstrap_confidence_interval
export calculate_statistical_significance, calculate_performance_trend
export enhanced_backtest_results, standard_normal_cdf

# ============================================================================
# Strategy Validation Functions (Task 2.2)
# ============================================================================

"""
    StrategyValidationError

Custom exception for strategy validation errors.
"""
struct StrategyValidationError <: Exception
    strategy_name::String
    validation_errors::Vector{String}
end

function Base.showerror(io::IO, e::StrategyValidationError)
    print(io, "策略驗證錯誤 '$(e.strategy_name)': ")
    for (i, error) in enumerate(e.validation_errors)
        if i > 1
            print(io, "; ")
        end
        print(io, error)
    end
end

"""
    validate_strategy(strategy::SelectionStrategy, historical_data::Vector{LotteryDraw}=LotteryDraw[])

Validate a strategy for logical consistency and parameter ranges.

# Arguments
- `strategy::SelectionStrategy`: Strategy to validate
- `historical_data::Vector{LotteryDraw}`: Historical data for validation (optional)

# Returns
- `Bool`: true if strategy is valid

# Throws
- `StrategyValidationError`: If strategy contains validation errors
"""
function validate_strategy(strategy::SelectionStrategy, 
                          historical_data::Vector{LotteryDraw}=LotteryDraw[])::Bool
    errors = String[]
    
    # Validate strategy name
    if isempty(strip(strategy.name))
        push!(errors, "策略名稱不能為空")
    end
    
    # Validate criteria
    if isempty(strategy.criteria)
        push!(errors, "策略必須至少包含一個選擇條件")
    end
    
    # Validate individual criteria
    for (i, criterion) in enumerate(strategy.criteria)
        try
            validate_criterion(criterion, historical_data)
        catch e
            push!(errors, "條件 $i 無效: $(e.msg)")
        end
    end
    
    # Validate combination logic compatibility
    logic_errors = validate_combination_logic(strategy.criteria, strategy.combination_logic)
    append!(errors, logic_errors)
    
    # Validate parameters
    param_errors = validate_strategy_parameters(strategy.parameters)
    append!(errors, param_errors)
    
    # Check data requirements
    data_errors = validate_data_requirements(strategy, historical_data)
    append!(errors, data_errors)
    
    if !isempty(errors)
        throw(StrategyValidationError(strategy.name, errors))
    end
    
    return true
end

"""
    validate_criterion(criterion::SelectionCriterion, historical_data::Vector{LotteryDraw})

Validate an individual criterion.
"""
function validate_criterion(criterion::SelectionCriterion, historical_data::Vector{LotteryDraw})
    if isa(criterion, FrequencyCriterion)
        if !isempty(historical_data) && length(historical_data) < criterion.analysis_periods
            throw(ArgumentError("歷史資料不足：需要 $(criterion.analysis_periods) 期，實際 $(length(historical_data)) 期"))
        end
    elseif isa(criterion, SkipCriterion)
        # Skip criterion validation is handled in constructor
    elseif isa(criterion, PairingCriterion)
        # Pairing criterion validation is handled in constructor
    end
end

"""
    validate_combination_logic(criteria::Vector{SelectionCriterion}, logic::CombinationLogic)

Validate combination logic compatibility with criteria.
"""
function validate_combination_logic(criteria::Vector{SelectionCriterion}, 
                                   logic::CombinationLogic)::Vector{String}
    errors = String[]
    
    if logic == WEIGHTED_LOGIC && length(criteria) < 2
        push!(errors, "加權邏輯需要至少兩個條件")
    end
    
    # Check for conflicting criteria
    freq_criteria = filter(c -> isa(c, FrequencyCriterion), criteria)
    if length(freq_criteria) > 1 && logic == AND_LOGIC
        # Check for overlapping frequency ranges
        for i in 1:length(freq_criteria)-1
            for j in i+1:length(freq_criteria)
                c1, c2 = freq_criteria[i], freq_criteria[j]
                if c1.max_frequency < c2.min_frequency || c2.max_frequency < c1.min_frequency
                    push!(errors, "頻率條件 $i 和 $j 的範圍不重疊，AND邏輯可能無結果")
                end
            end
        end
    end
    
    return errors
end

"""
    validate_strategy_parameters(parameters::Dict{String, Any})

Validate strategy parameters.
"""
function validate_strategy_parameters(parameters::Dict{String, Any})::Vector{String}
    errors = String[]
    
    # Check for reserved parameter names
    reserved_names = ["name", "description", "criteria", "logic", "created_date"]
    for name in keys(parameters)
        if name in reserved_names
            push!(errors, "參數名稱 '$name' 是保留字")
        end
    end
    
    # Validate specific parameter types and ranges
    if haskey(parameters, "max_selections") 
        max_sel = parameters["max_selections"]
        if !isa(max_sel, Int) || max_sel < 1 || max_sel > 39
            push!(errors, "max_selections 必須是 1-39 之間的整數")
        end
    end
    
    if haskey(parameters, "confidence_threshold")
        conf_thresh = parameters["confidence_threshold"]
        if !isa(conf_thresh, Real) || conf_thresh < 0.0 || conf_thresh > 1.0
            push!(errors, "confidence_threshold 必須是 0.0-1.0 之間的數值")
        end
    end
    
    return errors
end

"""
    validate_data_requirements(strategy::SelectionStrategy, historical_data::Vector{LotteryDraw})

Validate that historical data meets strategy requirements.
"""
function validate_data_requirements(strategy::SelectionStrategy, 
                                   historical_data::Vector{LotteryDraw})::Vector{String}
    errors = String[]
    
    # Check if strategy requires historical data
    requires_data = any(criterion -> isa(criterion, FrequencyCriterion) || isa(criterion, SkipCriterion) || isa(criterion, PairingCriterion), strategy.criteria)
    
    if requires_data && isempty(historical_data)
        push!(errors, "策略需要歷史資料但未提供")
        return errors
    end
    
    if isempty(historical_data)
        return errors  # No data to validate
    end
    
    # Check minimum data requirements
    min_periods_needed = 0
    for criterion in strategy.criteria
        if isa(criterion, FrequencyCriterion)
            min_periods_needed = max(min_periods_needed, criterion.analysis_periods)
        end
    end
    
    if length(historical_data) < min_periods_needed
        push!(errors, "歷史資料不足：需要至少 $min_periods_needed 期，實際 $(length(historical_data)) 期")
    end
    
    # Check data quality
    if any(draw -> length(draw.numbers) != 5, historical_data)
        push!(errors, "歷史資料格式錯誤：每期應包含5個號碼")
    end
    
    if any(draw -> any(n -> n < 1 || n > 39, draw.numbers), historical_data)
        push!(errors, "歷史資料包含無效號碼：號碼應在1-39範圍內")
    end
    
    return errors
end

# ============================================================================
# Strategy Serialization Functions (Task 2.2)
# ============================================================================

using Serialization

"""
    serialize_strategy(strategy::SelectionStrategy, filepath::String, validate::Bool=true)

Serialize a strategy to a file using Julia's built-in serialization.

# Arguments
- `strategy::SelectionStrategy`: Strategy to serialize
- `filepath::String`: File path for serialization
- `validate::Bool`: Whether to validate strategy before serialization (default: true)

# Throws
- `SystemError`: If file cannot be written
- `StrategyValidationError`: If strategy is invalid and validate=true
"""
function serialize_strategy(strategy::SelectionStrategy, filepath::String, validate::Bool=true)
    # Validate strategy before serialization
    if validate
        validate_strategy(strategy)
    end
    
    # Ensure directory exists
    dir = dirname(filepath)
    if !isempty(dir) && !isdir(dir)
        mkpath(dir)
    end
    
    try
        open(filepath, "w") do io
            serialize(io, strategy)
        end
        @info "策略已保存到: $filepath"
    catch e
        throw(SystemError("無法保存策略到 $filepath: $(e.msg)"))
    end
end

"""
    deserialize_strategy(filepath::String, validate::Bool=true)

Deserialize a strategy from a file.

# Arguments
- `filepath::String`: File path for deserialization
- `validate::Bool`: Whether to validate deserialized strategy (default: true)

# Returns
- `SelectionStrategy`: Deserialized strategy

# Throws
- `SystemError`: If file cannot be read or doesn't exist
- `StrategyValidationError`: If deserialized strategy is invalid and validate=true
"""
function deserialize_strategy(filepath::String, validate::Bool=true)::SelectionStrategy
    if !isfile(filepath)
        throw(SystemError("策略檔案不存在: $filepath"))
    end
    
    try
        strategy = open(filepath, "r") do io
            deserialize(io)
        end
        
        # Validate deserialized strategy
        if !isa(strategy, SelectionStrategy)
            throw(ArgumentError("檔案不包含有效的策略資料"))
        end
        
        if validate
            validate_strategy(strategy)
        end
        @info "策略已從 $filepath 載入: $(strategy.name)"
        return strategy
        
    catch e
        if isa(e, StrategyValidationError)
            rethrow(e)
        else
            error_msg = hasfield(typeof(e), :msg) ? e.msg : string(e)
            throw(SystemError("無法載入策略從 $filepath: $error_msg"))
        end
    end
end

# ============================================================================
# Strategy Storage Management (Task 2.2)
# ============================================================================

"""
    save_strategy(strategy::SelectionStrategy, storage_dir::String="data/strategies", validate::Bool=true)

Save a strategy to the default storage directory with automatic naming.

# Arguments
- `strategy::SelectionStrategy`: Strategy to save
- `storage_dir::String`: Storage directory (default: "data/strategies")
- `validate::Bool`: Whether to validate strategy before saving (default: true)

# Returns
- `String`: Path where strategy was saved
"""
function save_strategy(strategy::SelectionStrategy, storage_dir::String="data/strategies", validate::Bool=true)::String
    # Create safe filename from strategy name
    safe_name = replace(strategy.name, r"[^\w\-_]" => "_")
    timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
    filename = "$(safe_name)_$(timestamp).jls"
    filepath = joinpath(storage_dir, filename)
    
    serialize_strategy(strategy, filepath, validate)
    return filepath
end

"""
    load_strategy(strategy_name::String, storage_dir::String="data/strategies", validate::Bool=true)

Load a strategy by name from the storage directory.

# Arguments
- `strategy_name::String`: Name of strategy to load
- `storage_dir::String`: Storage directory (default: "data/strategies")
- `validate::Bool`: Whether to validate loaded strategy (default: true)

# Returns
- `SelectionStrategy`: Loaded strategy

# Throws
- `ArgumentError`: If strategy not found or multiple matches
"""
function load_strategy(strategy_name::String, storage_dir::String="data/strategies", validate::Bool=true)::SelectionStrategy
    if !isdir(storage_dir)
        throw(ArgumentError("策略儲存目錄不存在: $storage_dir"))
    end
    
    # Find strategy files matching the name
    safe_name = replace(strategy_name, r"[^\w\-_]" => "_")
    pattern = Regex("^$(safe_name)_\\d{8}_\\d{6}\\.jls\$")
    
    matching_files = String[]
    for file in readdir(storage_dir)
        if occursin(pattern, file)
            push!(matching_files, joinpath(storage_dir, file))
        end
    end
    
    if isempty(matching_files)
        throw(ArgumentError("找不到策略: $strategy_name"))
    end
    
    if length(matching_files) > 1
        # Return the most recent one
        sort!(matching_files, rev=true)
        @warn "找到多個版本的策略 '$strategy_name'，載入最新版本: $(basename(matching_files[1]))"
    end
    
    return deserialize_strategy(matching_files[1], validate)
end

"""
    list_strategies(storage_dir::String="data/strategies")

List all available strategies in the storage directory.

# Arguments
- `storage_dir::String`: Storage directory (default: "data/strategies")

# Returns
- `Vector{Tuple{String, Date}}`: List of (strategy_name, created_date) tuples
"""
function list_strategies(storage_dir::String="data/strategies")::Vector{Tuple{String, Date}}
    if !isdir(storage_dir)
        return Tuple{String, Date}[]
    end
    
    strategies = Tuple{String, Date}[]
    
    for file in readdir(storage_dir)
        if endswith(file, ".jls")
            try
                filepath = joinpath(storage_dir, file)
                strategy = deserialize_strategy(filepath, false)  # Skip validation for listing
                push!(strategies, (strategy.name, strategy.created_date))
            catch e
                error_msg = hasfield(typeof(e), :msg) ? e.msg : string(e)
                @warn "無法載入策略檔案 $file: $error_msg"
            end
        end
    end
    
    return sort(strategies, by=x->x[2], rev=true)  # Sort by date, newest first
end

"""
    delete_strategy(strategy_name::String, storage_dir::String="data/strategies")

Delete a strategy from storage.

# Arguments
- `strategy_name::String`: Name of strategy to delete
- `storage_dir::String`: Storage directory (default: "data/strategies")

# Returns
- `Bool`: true if strategy was deleted
"""
function delete_strategy(strategy_name::String, storage_dir::String="data/strategies")::Bool
    if !isdir(storage_dir)
        return false
    end
    
    safe_name = replace(strategy_name, r"[^\w\-_]" => "_")
    pattern = Regex("^$(safe_name)_\\d{8}_\\d{6}\\.jls\$")
    
    deleted_count = 0
    for file in readdir(storage_dir)
        if occursin(pattern, file)
            filepath = joinpath(storage_dir, file)
            try
                rm(filepath)
                deleted_count += 1
                @info "已刪除策略檔案: $file"
            catch e
                @warn "無法刪除策略檔案 $file: $(e.msg)"
            end
        end
    end
    
    return deleted_count > 0
end

# ============================================================================
# Multi-Strategy Comparison Engine (Task 5.1)
# ============================================================================

"""
    StrategyComparison

Results from comparing multiple strategies side-by-side.

# Fields
- `strategies::Vector{String}`: Names of compared strategies
- `metrics_comparison::Dict{String, Dict{String, Float64}}`: Metrics for each strategy
- `statistical_tests::Dict{String, Float64}`: Statistical test results between strategies
- `best_performer::String`: Name of the best performing strategy
- `recommendation::String`: Recommendation based on analysis
- `comparison_date::Date`: Date when comparison was performed
- `total_executions::Int`: Total executions used for comparison
"""
struct StrategyComparison
    strategies::Vector{String}
    metrics_comparison::Dict{String, Dict{String, Float64}}
    statistical_tests::Dict{String, Float64}
    best_performer::String
    recommendation::String
    comparison_date::Date
    total_executions::Int
    
    function StrategyComparison(strategies::Vector{String}, 
                               metrics_comparison::Dict{String, Dict{String, Float64}},
                               statistical_tests::Dict{String, Float64},
                               best_performer::String,
                               recommendation::String,
                               comparison_date::Date=today(),
                               total_executions::Int=0)
        if length(strategies) < 2
            throw(ArgumentError("比較至少需要2個策略"))
        end
        if isempty(metrics_comparison)
            throw(ArgumentError("指標比較不能為空"))
        end
        if !(best_performer in strategies)
            throw(ArgumentError("最佳表現策略必須在策略列表中: $best_performer"))
        end
        
        new(strategies, metrics_comparison, statistical_tests, best_performer, 
            recommendation, comparison_date, total_executions)
    end
end

"""
    compare_strategies(strategy_results::Dict{String, Vector{BacktestExecution}})

Compare multiple strategies side-by-side with statistical analysis.

# Arguments
- `strategy_results::Dict{String, Vector{BacktestExecution}}`: Results for each strategy

# Returns
- `StrategyComparison`: Comprehensive comparison results

# Throws
- `ArgumentError`: If insufficient strategies or invalid data
"""
function compare_strategies(strategy_results::Dict{String, Vector{BacktestExecution}})::StrategyComparison
    if length(strategy_results) < 2
        throw(ArgumentError("比較至少需要2個策略的結果"))
    end
    
    # Validate that all strategies have results
    for (strategy_name, executions) in strategy_results
        if isempty(executions)
            throw(ArgumentError("策略 '$strategy_name' 沒有執行結果"))
        end
    end
    
    strategies = collect(keys(strategy_results))
    metrics_comparison = Dict{String, Dict{String, Float64}}()
    
    # Calculate metrics for each strategy
    for (strategy_name, executions) in strategy_results
        results = calculate_basic_metrics(executions)
        significance = calculate_statistical_significance(executions)
        
        metrics_comparison[strategy_name] = Dict{String, Float64}(
            "hit_rate" => results.hit_rate,
            "roi" => results.roi,
            "avg_hits_per_execution" => results.average_hits_per_execution,
            "max_consecutive_misses" => Float64(results.max_consecutive_misses),
            "max_consecutive_hits" => Float64(results.max_consecutive_hits),
            "total_executions" => Float64(results.total_executions),
            "total_hits" => Float64(results.total_hits),
            "chi_square_p_value" => significance["chi_square_p_value"],
            "hit_rate_p_value" => significance["hit_rate_p_value"],
            "is_significant_05" => significance["is_significant_05"]
        )
    end
    
    # Perform statistical tests between strategies
    statistical_tests = perform_strategy_statistical_tests(strategy_results)
    
    # Determine best performer and generate recommendation
    best_performer, recommendation = determine_best_strategy(metrics_comparison, statistical_tests)
    
    # Calculate total executions
    total_executions = sum(length(executions) for executions in values(strategy_results))
    
    return StrategyComparison(
        strategies, metrics_comparison, statistical_tests, 
        best_performer, recommendation, today(), total_executions
    )
end

"""
    perform_strategy_statistical_tests(strategy_results::Dict{String, Vector{BacktestExecution}})

Perform statistical tests to compare strategy performances.

# Arguments
- `strategy_results::Dict{String, Vector{BacktestExecution}}`: Results for each strategy

# Returns
- `Dict{String, Float64}`: Statistical test results
"""
function perform_strategy_statistical_tests(strategy_results::Dict{String, Vector{BacktestExecution}})::Dict{String, Float64}
    strategies = collect(keys(strategy_results))
    tests = Dict{String, Float64}()
    
    # Pairwise comparisons between strategies
    for i in 1:length(strategies)
        for j in (i+1):length(strategies)
            strategy1 = strategies[i]
            strategy2 = strategies[j]
            
            executions1 = strategy_results[strategy1]
            executions2 = strategy_results[strategy2]
            
            # Two-sample t-test for hit rates
            hits1 = [Float64(exec.hits) for exec in executions1]
            hits2 = [Float64(exec.hits) for exec in executions2]
            
            t_stat, p_value = two_sample_t_test(hits1, hits2)
            
            test_key = "$(strategy1)_vs_$(strategy2)_t_test_p_value"
            tests[test_key] = p_value
            
            test_key_stat = "$(strategy1)_vs_$(strategy2)_t_statistic"
            tests[test_key_stat] = t_stat
            
            # Effect size (Cohen's d)
            cohens_d = calculate_cohens_d(hits1, hits2)
            effect_key = "$(strategy1)_vs_$(strategy2)_cohens_d"
            tests[effect_key] = cohens_d
        end
    end
    
    # Overall ANOVA F-test if more than 2 strategies
    if length(strategies) > 2
        f_stat, f_p_value = one_way_anova(strategy_results)
        tests["anova_f_statistic"] = f_stat
        tests["anova_p_value"] = f_p_value
    end
    
    return tests
end

"""
    two_sample_t_test(sample1::Vector{Float64}, sample2::Vector{Float64})

Perform two-sample t-test assuming unequal variances (Welch's t-test).

# Arguments
- `sample1::Vector{Float64}`: First sample
- `sample2::Vector{Float64}`: Second sample

# Returns
- `Tuple{Float64, Float64}`: (t_statistic, p_value)
"""
function two_sample_t_test(sample1::Vector{Float64}, sample2::Vector{Float64})::Tuple{Float64, Float64}
    if isempty(sample1) || isempty(sample2)
        return (0.0, 1.0)
    end
    
    n1, n2 = length(sample1), length(sample2)
    mean1, mean2 = mean(sample1), mean(sample2)
    var1, var2 = var(sample1), var(sample2)
    
    # Handle zero variance cases
    if var1 == 0.0 && var2 == 0.0
        return mean1 == mean2 ? (0.0, 1.0) : (Inf, 0.0)
    end
    
    # Welch's t-test
    se = sqrt(var1/n1 + var2/n2)
    if se == 0.0
        return (0.0, 1.0)
    end
    
    t_stat = (mean1 - mean2) / se
    
    # Degrees of freedom for Welch's t-test
    if var1 == 0.0 || var2 == 0.0 || n1 <= 1 || n2 <= 1
        df = max(1.0, Float64(min(n1, n2) - 1))
    else
        df = (var1/n1 + var2/n2)^2 / ((var1/n1)^2/(n1-1) + (var2/n2)^2/(n2-1))
        df = max(1.0, df)  # Ensure df is at least 1
    end
    
    # Approximate p-value using t-distribution
    p_value = 2.0 * (1.0 - t_distribution_cdf(abs(t_stat), df))
    
    # Ensure p-value is valid
    if isnan(p_value) || isinf(p_value)
        p_value = 1.0
    end
    
    return (t_stat, p_value)
end

"""
    calculate_cohens_d(sample1::Vector{Float64}, sample2::Vector{Float64})

Calculate Cohen's d effect size between two samples.

# Arguments
- `sample1::Vector{Float64}`: First sample
- `sample2::Vector{Float64}`: Second sample

# Returns
- `Float64`: Cohen's d effect size
"""
function calculate_cohens_d(sample1::Vector{Float64}, sample2::Vector{Float64})::Float64
    if isempty(sample1) || isempty(sample2)
        return 0.0
    end
    
    n1, n2 = length(sample1), length(sample2)
    mean1, mean2 = mean(sample1), mean(sample2)
    var1, var2 = var(sample1), var(sample2)
    
    # Pooled standard deviation
    pooled_sd = sqrt(((n1-1)*var1 + (n2-1)*var2) / (n1+n2-2))
    
    if pooled_sd == 0.0
        return 0.0
    end
    
    return (mean1 - mean2) / pooled_sd
end

"""
    one_way_anova(strategy_results::Dict{String, Vector{BacktestExecution}})

Perform one-way ANOVA to test if there are significant differences between strategies.

# Arguments
- `strategy_results::Dict{String, Vector{BacktestExecution}}`: Results for each strategy

# Returns
- `Tuple{Float64, Float64}`: (f_statistic, p_value)
"""
function one_way_anova(strategy_results::Dict{String, Vector{BacktestExecution}})::Tuple{Float64, Float64}
    if length(strategy_results) < 2
        return (0.0, 1.0)
    end
    
    # Collect all hit values by strategy
    strategy_hits = Dict{String, Vector{Float64}}()
    for (strategy_name, executions) in strategy_results
        strategy_hits[strategy_name] = [Float64(exec.hits) for exec in executions]
    end
    
    # Calculate overall mean
    all_hits = vcat(values(strategy_hits)...)
    overall_mean = mean(all_hits)
    total_n = length(all_hits)
    
    # Calculate between-group sum of squares (SSB)
    ssb = 0.0
    for (strategy_name, hits) in strategy_hits
        group_mean = mean(hits)
        group_n = length(hits)
        ssb += group_n * (group_mean - overall_mean)^2
    end
    
    # Calculate within-group sum of squares (SSW)
    ssw = 0.0
    for (strategy_name, hits) in strategy_hits
        group_mean = mean(hits)
        for hit in hits
            ssw += (hit - group_mean)^2
        end
    end
    
    # Degrees of freedom
    df_between = length(strategy_results) - 1
    df_within = total_n - length(strategy_results)
    
    if df_between == 0 || df_within == 0 || ssw == 0.0
        return (0.0, 1.0)
    end
    
    # F-statistic
    msb = ssb / df_between
    msw = ssw / df_within
    f_stat = msb / msw
    
    # Approximate p-value using F-distribution
    p_value = f_distribution_p_value(f_stat, df_between, df_within)
    
    return (f_stat, p_value)
end

"""
    t_distribution_cdf(t::Float64, df::Float64)

Approximate cumulative distribution function for t-distribution.

# Arguments
- `t::Float64`: t-statistic
- `df::Float64`: Degrees of freedom

# Returns
- `Float64`: Cumulative probability
"""
function t_distribution_cdf(t::Float64, df::Float64)::Float64
    if df <= 0
        return 0.5
    end
    
    # For large df, approximate with standard normal
    if df >= 30
        return standard_normal_cdf(t)
    end
    
    # Simple approximation for small df
    # Using the fact that t-distribution approaches normal as df increases
    adjustment = 1.0 + t^2 / (4*df)
    adjusted_t = t / sqrt(adjustment)
    
    return standard_normal_cdf(adjusted_t)
end

"""
    f_distribution_p_value(f::Float64, df1::Int, df2::Int)

Approximate p-value for F-distribution.

# Arguments
- `f::Float64`: F-statistic
- `df1::Int`: Numerator degrees of freedom
- `df2::Int`: Denominator degrees of freedom

# Returns
- `Float64`: Approximate p-value
"""
function f_distribution_p_value(f::Float64, df1::Int, df2::Int)::Float64
    if f <= 0.0 || df1 <= 0 || df2 <= 0
        return 1.0
    end
    
    # Simple approximation based on critical values
    # For most practical purposes in lottery analysis
    if f < 1.0
        return 0.8
    elseif f < 2.0
        return 0.3
    elseif f < 3.0
        return 0.1
    elseif f < 4.0
        return 0.05
    elseif f < 6.0
        return 0.01
    else
        return 0.001
    end
end

"""
    determine_best_strategy(metrics_comparison::Dict{String, Dict{String, Float64}}, 
                           statistical_tests::Dict{String, Float64})

Determine the best performing strategy and generate recommendation.

# Arguments
- `metrics_comparison::Dict{String, Dict{String, Float64}}`: Metrics for each strategy
- `statistical_tests::Dict{String, Float64}`: Statistical test results

# Returns
- `Tuple{String, String}`: (best_strategy_name, recommendation_text)
"""
function determine_best_strategy(metrics_comparison::Dict{String, Dict{String, Float64}}, 
                                statistical_tests::Dict{String, Float64})::Tuple{String, String}
    if isempty(metrics_comparison)
        return ("", "無法確定最佳策略：沒有可比較的指標")
    end
    
    strategies = collect(keys(metrics_comparison))
    
    # Multi-criteria ranking
    strategy_scores = Dict{String, Float64}()
    
    for strategy in strategies
        metrics = metrics_comparison[strategy]
        score = 0.0
        
        # Hit rate (40% weight)
        hit_rate = get(metrics, "hit_rate", 0.0)
        score += 0.4 * hit_rate
        
        # ROI (30% weight)
        roi = get(metrics, "roi", 0.0)
        # Normalize ROI to 0-1 scale (assuming reasonable range -1 to 10)
        normalized_roi = max(0.0, min(1.0, (roi + 1.0) / 11.0))
        score += 0.3 * normalized_roi
        
        # Statistical significance (20% weight)
        is_significant = get(metrics, "is_significant_05", 0.0)
        score += 0.2 * is_significant
        
        # Consistency (10% weight) - inverse of max consecutive misses
        max_misses = get(metrics, "max_consecutive_misses", 0.0)
        total_executions = get(metrics, "total_executions", 1.0)
        consistency = 1.0 - (max_misses / total_executions)
        score += 0.1 * max(0.0, consistency)
        
        strategy_scores[strategy] = score
    end
    
    # Find best strategy
    best_strategy = ""
    best_score = -1.0
    for (strategy, score) in strategy_scores
        if score > best_score
            best_score = score
            best_strategy = strategy
        end
    end
    
    # Generate recommendation
    recommendation = generate_strategy_recommendation(best_strategy, metrics_comparison, 
                                                    statistical_tests, strategy_scores)
    
    return (best_strategy, recommendation)
end

"""
    generate_strategy_recommendation(best_strategy::String, 
                                   metrics_comparison::Dict{String, Dict{String, Float64}},
                                   statistical_tests::Dict{String, Float64},
                                   strategy_scores::Dict{String, Float64})

Generate detailed recommendation text based on analysis results.

# Arguments
- `best_strategy::String`: Name of the best performing strategy
- `metrics_comparison::Dict{String, Dict{String, Float64}}`: Metrics for each strategy
- `statistical_tests::Dict{String, Float64}`: Statistical test results
- `strategy_scores::Dict{String, Float64}`: Calculated scores for each strategy

# Returns
- `String`: Detailed recommendation text
"""
function generate_strategy_recommendation(best_strategy::String, 
                                        metrics_comparison::Dict{String, Dict{String, Float64}},
                                        statistical_tests::Dict{String, Float64},
                                        strategy_scores::Dict{String, Float64})::String
    if isempty(best_strategy)
        return "無法生成建議：沒有有效的策略比較結果"
    end
    
    best_metrics = metrics_comparison[best_strategy]
    best_score = get(strategy_scores, best_strategy, 0.0)
    
    recommendation = "## 策略比較建議\n\n"
    recommendation *= "**推薦策略**: $best_strategy\n"
    recommendation *= "**綜合評分**: $(round(best_score, digits=3))\n\n"
    
    # Performance summary
    hit_rate = get(best_metrics, "hit_rate", 0.0)
    roi = get(best_metrics, "roi", 0.0)
    avg_hits = get(best_metrics, "avg_hits_per_execution", 0.0)
    
    recommendation *= "### 表現摘要\n"
    recommendation *= "- 命中率: $(round(hit_rate * 100, digits=2))%\n"
    recommendation *= "- 投資報酬率: $(round(roi * 100, digits=2))%\n"
    recommendation *= "- 平均每次命中數: $(round(avg_hits, digits=2))\n\n"
    
    # Statistical significance
    is_significant = get(best_metrics, "is_significant_05", 0.0)
    p_value = get(best_metrics, "hit_rate_p_value", 1.0)
    
    recommendation *= "### 統計顯著性\n"
    if is_significant > 0.5
        recommendation *= "- ✅ 策略表現具統計顯著性 (p < 0.05)\n"
        recommendation *= "- p值: $(round(p_value, digits=4))\n"
    else
        recommendation *= "- ⚠️ 策略表現未達統計顯著性\n"
        recommendation *= "- p值: $(round(p_value, digits=4))\n"
        recommendation *= "- 建議增加測試期數以提高統計可信度\n"
    end
    
    # Risk assessment
    max_misses = get(best_metrics, "max_consecutive_misses", 0.0)
    total_executions = get(best_metrics, "total_executions", 1.0)
    
    recommendation *= "\n### 風險評估\n"
    recommendation *= "- 最大連續未中次數: $(Int(max_misses))\n"
    
    if max_misses / total_executions > 0.3
        recommendation *= "- ⚠️ 高風險：連續未中期較長，建議謹慎使用\n"
    elseif max_misses / total_executions > 0.15
        recommendation *= "- ⚡ 中等風險：需要適當的資金管理\n"
    else
        recommendation *= "- ✅ 低風險：表現相對穩定\n"
    end
    
    # Comparison with other strategies
    if length(strategy_scores) > 1
        recommendation *= "\n### 與其他策略比較\n"
        sorted_strategies = sort(collect(strategy_scores), by=x->x[2], rev=true)
        
        for (i, (strategy, score)) in enumerate(sorted_strategies[1:min(3, end)])
            if strategy == best_strategy
                recommendation *= "1. **$strategy** (推薦) - 評分: $(round(score, digits=3))\n"
            else
                recommendation *= "$(i+1). $strategy - 評分: $(round(score, digits=3))\n"
            end
        end
    end
    
    # Usage recommendations
    recommendation *= "\n### 使用建議\n"
    if hit_rate > 0.25  # Above random expectation
        recommendation *= "- ✅ 建議採用此策略進行實際投注\n"
        if roi > 0
            recommendation *= "- 💰 策略具有正期望值，長期使用可能獲利\n"
        end
    else
        recommendation *= "- ⚠️ 策略表現接近隨機水準，建議進一步優化\n"
    end
    
    if max_misses > 10
        recommendation *= "- 💡 建議設定停損點，避免長期連續虧損\n"
    end
    
    recommendation *= "- 📊 建議定期重新評估策略表現\n"
    recommendation *= "- 🔄 可考慮結合多個策略以分散風險\n"
    
    return recommendation
end

"""
    rank_strategies_by_criteria(metrics_comparison::Dict{String, Dict{String, Float64}}, 
                               criterion::String)

Rank strategies by a specific criterion.

# Arguments
- `metrics_comparison::Dict{String, Dict{String, Float64}}`: Metrics for each strategy
- `criterion::String`: Criterion to rank by (e.g., "hit_rate", "roi")

# Returns
- `Vector{Tuple{String, Float64}}`: Ranked list of (strategy_name, criterion_value)
"""
function rank_strategies_by_criteria(metrics_comparison::Dict{String, Dict{String, Float64}}, 
                                   criterion::String)::Vector{Tuple{String, Float64}}
    rankings = Tuple{String, Float64}[]
    
    for (strategy_name, metrics) in metrics_comparison
        value = get(metrics, criterion, 0.0)
        push!(rankings, (strategy_name, value))
    end
    
    # Sort by criterion value (descending)
    sort!(rankings, by=x->x[2], rev=true)
    
    return rankings
end

"""
    format_comparison_results(comparison::StrategyComparison)

Format strategy comparison results for display.

# Arguments
- `comparison::StrategyComparison`: Comparison results to format

# Returns
- `String`: Formatted comparison results
"""
function format_comparison_results(comparison::StrategyComparison)::String
    output = "# 策略比較結果\n\n"
    output *= "**比較日期**: $(comparison.comparison_date)\n"
    output *= "**比較策略數**: $(length(comparison.strategies))\n"
    output *= "**總執行次數**: $(comparison.total_executions)\n"
    output *= "**最佳策略**: $(comparison.best_performer)\n\n"
    
    # Metrics table
    output *= "## 詳細指標比較\n\n"
    output *= "| 策略名稱 | 命中率 | ROI | 平均命中數 | 最大連續未中 | 統計顯著性 |\n"
    output *= "|---------|--------|-----|-----------|-------------|----------|\n"
    
    for strategy in comparison.strategies
        metrics = comparison.metrics_comparison[strategy]
        hit_rate = round(get(metrics, "hit_rate", 0.0) * 100, digits=2)
        roi = round(get(metrics, "roi", 0.0) * 100, digits=2)
        avg_hits = round(get(metrics, "avg_hits_per_execution", 0.0), digits=2)
        max_misses = Int(get(metrics, "max_consecutive_misses", 0.0))
        is_sig = get(metrics, "is_significant_05", 0.0) > 0.5 ? "是" : "否"
        
        marker = strategy == comparison.best_performer ? "**" : ""
        output *= "| $marker$strategy$marker | $hit_rate% | $roi% | $avg_hits | $max_misses | $is_sig |\n"
    end
    
    output *= "\n$(comparison.recommendation)\n"
    
    return output
end

# ============================================================================
# Performance Visualization System (Task 5.2)
# ============================================================================

# Check if Plots.jl is available
global PLOTS_AVAILABLE = false
try
    using Plots
    global PLOTS_AVAILABLE = true
    
    # Try to use PlotlyJS backend for better HTML output
    try
        using PlotlyJS
        plotlyjs()
        @info "Performance visualization using PlotlyJS backend"
    catch
        # Fallback to GR backend
        try
            gr()
            @info "Performance visualization using GR backend"
        catch
            @warn "Using default Plots backend for visualization"
        end
    end
catch e
    @warn "Plots.jl not available. Visualization features disabled. Install with: Pkg.add([\"Plots\", \"PlotlyJS\"])"
end

"""
    create_performance_trend_chart(executions::Vector{BacktestExecution}, 
                                  window_size::Int=20, 
                                  title::String="Strategy Performance Trend")

Create a performance trend chart showing hit rates over time.

# Arguments
- `executions::Vector{BacktestExecution}`: Execution results to visualize
- `window_size::Int`: Rolling window size for trend calculation (default: 20)
- `title::String`: Chart title

# Returns
- `Union{Plots.Plot, Nothing}`: Chart object or nothing if Plots unavailable
"""
function create_performance_trend_chart(executions::Vector{BacktestExecution}, 
                                       window_size::Int=20, 
                                       title::String="Strategy Performance Trend")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot create performance trend chart."
        return nothing
    end
    
    if isempty(executions)
        @warn "No execution data provided for trend chart."
        return nothing
    end
    
    if window_size <= 0
        @warn "Invalid window size: $window_size. Using default window size of 5."
        window_size = 5
    end
    
    if length(executions) < window_size
        @warn "Insufficient data for rolling window analysis. Need at least $window_size executions."
        if length(executions) == 0
            return nothing
        end
        window_size = length(executions)
    end
    
    # Calculate rolling hit rates
    dates = [exec.execution_date for exec in executions]
    rolling_hit_rates = Float64[]
    rolling_dates = Date[]
    
    for i in window_size:length(executions)
        window_executions = executions[(i-window_size+1):i]
        window_hits = sum(exec -> exec.hits, window_executions)
        window_hit_rate = window_hits / (window_size * 5) * 100  # Convert to percentage
        push!(rolling_hit_rates, window_hit_rate)
        push!(rolling_dates, dates[i])
    end
    
    # Expected random hit rate (5/39 ≈ 12.82%)
    expected_rate = (5.0 / 39.0) * 100
    
    # Create the plot
    p = Plots.plot(rolling_dates, rolling_hit_rates,
                   title=title,
                   xlabel="Date",
                   ylabel="Hit Rate (%)",
                   label="Rolling Hit Rate ($(window_size)-period)",
                   linewidth=2,
                   color=:blue,
                   grid=true,
                   size=(1000, 600))
    
    # Add expected random rate line
    Plots.hline!([expected_rate], 
                 label="Expected Random Rate",
                 linestyle=:dash,
                 color=:red,
                 linewidth=2)
    
    # Add trend line if enough data points
    if length(rolling_hit_rates) >= 10
        # Simple linear trend
        x_vals = 1:length(rolling_hit_rates)
        trend_coef = sum((x_vals .- mean(x_vals)) .* (rolling_hit_rates .- mean(rolling_hit_rates))) / 
                    sum((x_vals .- mean(x_vals)).^2)
        trend_intercept = mean(rolling_hit_rates) - trend_coef * mean(x_vals)
        trend_line = trend_intercept .+ trend_coef .* x_vals
        
        Plots.plot!(rolling_dates, trend_line,
                    label="Trend Line",
                    linestyle=:dot,
                    color=:green,
                    linewidth=2)
    end
    
    return p
end

"""
    create_cumulative_return_chart(executions::Vector{BacktestExecution}, 
                                  title::String="Cumulative Returns")

Create a cumulative return chart showing investment performance over time.

# Arguments
- `executions::Vector{BacktestExecution}`: Execution results to visualize
- `title::String`: Chart title

# Returns
- `Union{Plots.Plot, Nothing}`: Chart object or nothing if Plots unavailable
"""
function create_cumulative_return_chart(executions::Vector{BacktestExecution}, 
                                       title::String="Cumulative Returns")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot create cumulative return chart."
        return nothing
    end
    
    if isempty(executions)
        @warn "No execution data provided for return chart."
        return nothing
    end
    
    # Payout structure for Pick 5 from 39
    payout_table = Dict{Int, Float64}(
        0 => 0.0, 1 => 2.0, 2 => 10.0, 3 => 100.0, 4 => 1000.0, 5 => 100000.0
    )
    
    dates = [exec.execution_date for exec in executions]
    cumulative_investment = Float64[]
    cumulative_returns = Float64[]
    cumulative_profit = Float64[]
    
    total_investment = 0.0
    total_returns = 0.0
    
    for (i, exec) in enumerate(executions)
        total_investment += 1.0  # Assume 1 unit cost per execution
        total_returns += get(payout_table, exec.hits, 0.0)
        
        push!(cumulative_investment, total_investment)
        push!(cumulative_returns, total_returns)
        push!(cumulative_profit, total_returns - total_investment)
    end
    
    # Create the plot
    p = Plots.plot(dates, cumulative_profit,
                   title=title,
                   xlabel="Date",
                   ylabel="Cumulative Profit/Loss",
                   label="Net Profit/Loss",
                   linewidth=2,
                   color=:blue,
                   grid=true,
                   size=(1000, 600))
    
    # Add zero line
    Plots.hline!([0], 
                 label="Break Even",
                 linestyle=:dash,
                 color=:black,
                 linewidth=1)
    
    # Add investment and return lines
    Plots.plot!(dates, cumulative_investment,
                label="Total Investment",
                linestyle=:dot,
                color=:red,
                linewidth=2)
    
    Plots.plot!(dates, cumulative_returns,
                label="Total Returns",
                linestyle=:dot,
                color=:green,
                linewidth=2)
    
    return p
end

"""
    create_hit_distribution_chart(executions::Vector{BacktestExecution}, 
                                 title::String="Hit Distribution")

Create a distribution chart showing the frequency of different hit counts.

# Arguments
- `executions::Vector{BacktestExecution}`: Execution results to visualize
- `title::String`: Chart title

# Returns
- `Union{Plots.Plot, Nothing}`: Chart object or nothing if Plots unavailable
"""
function create_hit_distribution_chart(executions::Vector{BacktestExecution}, 
                                      title::String="Hit Distribution")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot create hit distribution chart."
        return nothing
    end
    
    if isempty(executions)
        @warn "No execution data provided for distribution chart."
        return nothing
    end
    
    # Calculate hit distribution
    hit_counts = Dict{Int, Int}()
    for i in 0:5
        hit_counts[i] = count(exec -> exec.hits == i, executions)
    end
    
    # Calculate expected distribution (hypergeometric)
    total_executions = length(executions)
    total_combinations = binomial(39, 5)
    expected_counts = Float64[]
    
    for k in 0:5
        if k <= 5 && (5-k) <= 34
            prob = (binomial(5, k) * binomial(34, 5-k)) / total_combinations
            push!(expected_counts, prob * total_executions)
        else
            push!(expected_counts, 0.0)
        end
    end
    
    # Create the plot
    hit_labels = ["0 hits", "1 hit", "2 hits", "3 hits", "4 hits", "5 hits"]
    observed_counts = [hit_counts[i] for i in 0:5]
    
    p = Plots.bar(hit_labels, observed_counts,
                  title=title,
                  xlabel="Number of Hits",
                  ylabel="Frequency",
                  label="Observed",
                  color=:lightblue,
                  alpha=0.7,
                  size=(800, 600))
    
    # Add expected distribution
    Plots.bar!(hit_labels, expected_counts,
               label="Expected (Random)",
               color=:red,
               alpha=0.5)
    
    return p
end

"""
    create_drawdown_chart(executions::Vector{BacktestExecution}, 
                         title::String="Drawdown Analysis")

Create a drawdown chart showing the largest losses from peak performance.

# Arguments
- `executions::Vector{BacktestExecution}`: Execution results to visualize
- `title::String`: Chart title

# Returns
- `Union{Plots.Plot, Nothing}`: Chart object or nothing if Plots unavailable
"""
function create_drawdown_chart(executions::Vector{BacktestExecution}, 
                              title::String="Drawdown Analysis")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot create drawdown chart."
        return nothing
    end
    
    if isempty(executions)
        @warn "No execution data provided for drawdown chart."
        return nothing
    end
    
    # Calculate cumulative returns
    payout_table = Dict{Int, Float64}(
        0 => 0.0, 1 => 2.0, 2 => 10.0, 3 => 100.0, 4 => 1000.0, 5 => 100000.0
    )
    
    dates = [exec.execution_date for exec in executions]
    cumulative_profit = Float64[]
    running_max = Float64[]
    drawdown = Float64[]
    
    total_investment = 0.0
    total_returns = 0.0
    peak_profit = 0.0
    
    for exec in executions
        total_investment += 1.0
        total_returns += get(payout_table, exec.hits, 0.0)
        current_profit = total_returns - total_investment
        
        peak_profit = max(peak_profit, current_profit)
        current_drawdown = current_profit - peak_profit
        
        push!(cumulative_profit, current_profit)
        push!(running_max, peak_profit)
        push!(drawdown, current_drawdown)
    end
    
    # Create the plot
    p = Plots.plot(dates, drawdown,
                   title=title,
                   xlabel="Date",
                   ylabel="Drawdown",
                   label="Drawdown",
                   linewidth=2,
                   color=:red,
                   fill=(0, :red, 0.3),
                   grid=true,
                   size=(1000, 600))
    
    # Add zero line
    Plots.hline!([0], 
                 label="No Drawdown",
                 linestyle=:dash,
                 color=:black,
                 linewidth=1)
    
    return p
end

"""
    create_strategy_comparison_chart(comparison::StrategyComparison, 
                                   metric::String="hit_rate",
                                   title::String="Strategy Comparison")

Create a comparison chart for multiple strategies.

# Arguments
- `comparison::StrategyComparison`: Comparison results to visualize
- `metric::String`: Metric to compare ("hit_rate", "roi", "avg_hits_per_execution")
- `title::String`: Chart title

# Returns
- `Union{Plots.Plot, Nothing}`: Chart object or nothing if Plots unavailable
"""
function create_strategy_comparison_chart(comparison::StrategyComparison, 
                                        metric::String="hit_rate",
                                        title::String="Strategy Comparison")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot create comparison chart."
        return nothing
    end
    
    if isempty(comparison.strategies)
        @warn "No strategies provided for comparison chart."
        return nothing
    end
    
    # Extract metric values
    strategy_names = comparison.strategies
    metric_values = Float64[]
    
    for strategy in strategy_names
        if haskey(comparison.metrics_comparison, strategy)
            value = get(comparison.metrics_comparison[strategy], metric, 0.0)
            # Convert hit_rate to percentage
            if metric == "hit_rate"
                value *= 100
            elseif metric == "roi"
                value *= 100
            end
            push!(metric_values, value)
        else
            push!(metric_values, 0.0)
        end
    end
    
    # Determine colors (highlight best performer)
    colors = [:lightblue for _ in strategy_names]
    best_idx = findfirst(s -> s == comparison.best_performer, strategy_names)
    if best_idx !== nothing
        colors[best_idx] = :gold
    end
    
    # Create the plot
    ylabel_text = if metric == "hit_rate"
        "Hit Rate (%)"
    elseif metric == "roi"
        "ROI (%)"
    elseif metric == "avg_hits_per_execution"
        "Average Hits per Execution"
    else
        metric
    end
    
    p = Plots.bar(strategy_names, metric_values,
                  title=title,
                  xlabel="Strategy",
                  ylabel=ylabel_text,
                  label="",
                  color=colors,
                  size=(800, 600),
                  xrotation=45)
    
    # Add value labels on bars
    for (i, value) in enumerate(metric_values)
        Plots.annotate!(i, value + maximum(metric_values) * 0.02, 
                       Plots.text(string(round(value, digits=2)), 10, :center))
    end
    
    return p
end

"""
    save_performance_charts(executions::Vector{BacktestExecution}, 
                           strategy_name::String,
                           output_dir::String="charts/backtesting")

Save all performance charts for a strategy to HTML files.

# Arguments
- `executions::Vector{BacktestExecution}`: Execution results to visualize
- `strategy_name::String`: Name of the strategy
- `output_dir::String`: Output directory for charts (default: "charts/backtesting")

# Returns
- `Vector{String}`: List of saved file paths
"""
function save_performance_charts(executions::Vector{BacktestExecution}, 
                                strategy_name::String,
                                output_dir::String="charts/backtesting")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot save performance charts."
        return String[]
    end
    
    if isempty(executions)
        @warn "No execution data provided for chart generation."
        return String[]
    end
    
    # Create output directory if it doesn't exist
    if !isdir(output_dir)
        mkpath(output_dir)
    end
    
    saved_files = String[]
    timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
    safe_strategy_name = replace(strategy_name, r"[^\w\-_]" => "_")
    
    try
        # 1. Performance trend chart
        trend_chart = create_performance_trend_chart(executions, 20, 
                                                    "$strategy_name - Performance Trend")
        if trend_chart !== nothing
            filename = "$(safe_strategy_name)_performance_trend_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(trend_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved performance trend chart: $filepath"
        end
        
        # 2. Cumulative return chart
        return_chart = create_cumulative_return_chart(executions, 
                                                     "$strategy_name - Cumulative Returns")
        if return_chart !== nothing
            filename = "$(safe_strategy_name)_cumulative_returns_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(return_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved cumulative return chart: $filepath"
        end
        
        # 3. Hit distribution chart
        dist_chart = create_hit_distribution_chart(executions, 
                                                  "$strategy_name - Hit Distribution")
        if dist_chart !== nothing
            filename = "$(safe_strategy_name)_hit_distribution_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(dist_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved hit distribution chart: $filepath"
        end
        
        # 4. Drawdown chart
        drawdown_chart = create_drawdown_chart(executions, 
                                              "$strategy_name - Drawdown Analysis")
        if drawdown_chart !== nothing
            filename = "$(safe_strategy_name)_drawdown_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(drawdown_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved drawdown chart: $filepath"
        end
        
    catch e
        @error "Error saving performance charts: $e"
    end
    
    return saved_files
end

"""
    save_comparison_charts(comparison::StrategyComparison,
                          output_dir::String="charts/backtesting")

Save strategy comparison charts to HTML files.

# Arguments
- `comparison::StrategyComparison`: Comparison results to visualize
- `output_dir::String`: Output directory for charts (default: "charts/backtesting")

# Returns
- `Vector{String}`: List of saved file paths
"""
function save_comparison_charts(comparison::StrategyComparison,
                               output_dir::String="charts/backtesting")
    if !PLOTS_AVAILABLE
        @warn "Plots.jl not available. Cannot save comparison charts."
        return String[]
    end
    
    # Create output directory if it doesn't exist
    if !isdir(output_dir)
        mkpath(output_dir)
    end
    
    saved_files = String[]
    timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
    
    try
        # Hit rate comparison
        hit_rate_chart = create_strategy_comparison_chart(comparison, "hit_rate", 
                                                         "Strategy Hit Rate Comparison")
        if hit_rate_chart !== nothing
            filename = "strategy_comparison_hit_rate_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(hit_rate_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved hit rate comparison chart: $filepath"
        end
        
        # ROI comparison
        roi_chart = create_strategy_comparison_chart(comparison, "roi", 
                                                    "Strategy ROI Comparison")
        if roi_chart !== nothing
            filename = "strategy_comparison_roi_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(roi_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved ROI comparison chart: $filepath"
        end
        
        # Average hits comparison
        avg_hits_chart = create_strategy_comparison_chart(comparison, "avg_hits_per_execution", 
                                                         "Strategy Average Hits Comparison")
        if avg_hits_chart !== nothing
            filename = "strategy_comparison_avg_hits_$(timestamp).html"
            filepath = joinpath(output_dir, filename)
            Plots.savefig(avg_hits_chart, filepath)
            push!(saved_files, filepath)
            @info "Saved average hits comparison chart: $filepath"
        end
        
    catch e
        @error "Error saving comparison charts: $e"
    end
    
    return saved_files
end

export create_performance_trend_chart, create_cumulative_return_chart
export create_hit_distribution_chart, create_drawdown_chart
export create_strategy_comparison_chart
export save_performance_charts, save_comparison_charts

export StrategyComparison, compare_strategies
export perform_strategy_statistical_tests, determine_best_strategy
export rank_strategies_by_criteria, format_comparison_results
export two_sample_t_test, calculate_cohens_d, one_way_anova

export SelectionStrategy, StrategyValidationError
export validate_strategy, serialize_strategy, deserialize_strategy
export save_strategy, load_strategy, list_strategies, delete_strategy

export HTMLReporter, generate_report

# Memory optimization exports
export DataStreamer, MemoryMonitor, ChunkedDataProcessor
export create_data_streamer, process_chunked_data, monitor_memory_usage
export StreamingBacktestWindow, create_streaming_windows
export AutoMemoryOptimizer, auto_optimize
export simulate_strategy_memory_efficient, simulate_strategy_chunked

# Parallel processing exports
export ParallelBacktestExecutor, ThreadSafeResults, ProgressAggregator
export execute_parallel_backtest, create_thread_safe_results
export ParallelConfiguration, configure_parallel_execution
export create_parallel_executor
export simulate_strategies_parallel, compare_strategies_parallel

# ============================================================================
# 記憶體高效回測執行 (Task 7.1)
# ============================================================================

"""
    simulate_strategy_memory_efficient(strategy::SelectionStrategy,
                                      historical_data::Vector{LotteryDraw},
                                      chunk_size::Int=1000,
                                      memory_limit_mb::Float64=512.0)

記憶體高效的策略模擬，適用於大型資料集。

# Arguments
- `strategy::SelectionStrategy`: 要測試的策略
- `historical_data::Vector{LotteryDraw}`: 歷史資料
- `chunk_size::Int`: 分塊大小
- `memory_limit_mb::Float64`: 記憶體限制 (MB)

# Returns
- `Vector{BacktestExecution}`: 執行結果
"""
function simulate_strategy_memory_efficient(strategy::SelectionStrategy,
                                           historical_data::Vector{LotteryDraw},
                                           chunk_size::Int=1000,
                                           memory_limit_mb::Float64=512.0)::Vector{BacktestExecution}
    if isempty(historical_data)
        throw(ArgumentError("歷史資料不能為空"))
    end

    @info "開始記憶體高效策略模擬: $(length(historical_data)) 期資料，分塊大小 $chunk_size"

    # 創建記憶體監控器
    memory_monitor = MemoryMonitor(memory_limit_mb, 0.8, true, 1.0)

    # 創建自動優化器
    optimizer = AutoMemoryOptimizer(memory_limit_mb)

    # 創建分塊處理器
    processor = ChunkedDataProcessor(
        chunk_size,
        min(100, chunk_size ÷ 10),  # 重疊大小為分塊大小的10%
        memory_monitor,
        chunk -> simulate_chunk(strategy, chunk)
    )

    # 執行分塊處理
    all_executions = process_chunked_data(processor, historical_data)

    @info "記憶體高效模擬完成: 共 $(length(all_executions)) 次執行"

    return all_executions
end

"""
    simulate_chunk(strategy::SelectionStrategy, chunk::Vector{LotteryDraw})

模擬單個資料塊。

# Arguments
- `strategy::SelectionStrategy`: 策略
- `chunk::Vector{LotteryDraw}`: 資料塊

# Returns
- `Vector{BacktestExecution}`: 執行結果
"""
function simulate_chunk(strategy::SelectionStrategy, chunk::Vector{LotteryDraw})::Vector{BacktestExecution}
    if length(chunk) < 2
        return BacktestExecution[]
    end

    executions = BacktestExecution[]

    # 使用前面的資料作為訓練，後面的作為驗證
    training_size = max(1, length(chunk) ÷ 2)

    for i in (training_size + 1):length(chunk)
        training_data = chunk[1:i-1]
        target_draw = chunk[i]

        try
            # 應用策略選擇號碼
            selected_numbers, confidence_scores = apply_strategy_selection(strategy, training_data)

            # 創建執行結果
            execution = BacktestExecution(strategy, target_draw.draw_date,
                                        selected_numbers, target_draw, confidence_scores)
            push!(executions, execution)

        catch e
            @warn "分塊模擬失敗: $e"
            continue
        end
    end

    return executions
end

"""
    simulate_strategy_chunked(strategy::SelectionStrategy,
                             data_streamer::DataStreamer{LotteryDraw},
                             window_size::Int=100)

使用資料串流進行策略模擬。

# Arguments
- `strategy::SelectionStrategy`: 策略
- `data_streamer::DataStreamer{LotteryDraw}`: 資料串流器
- `window_size::Int`: 滑動視窗大小

# Returns
- `Vector{BacktestExecution}`: 執行結果
"""
function simulate_strategy_chunked(strategy::SelectionStrategy,
                                  data_streamer::DataStreamer{LotteryDraw},
                                  window_size::Int=100)::Vector{BacktestExecution}
    @info "開始串流策略模擬: 視窗大小 $window_size"

    all_executions = BacktestExecution[]
    accumulated_data = LotteryDraw[]

    while has_more_data(data_streamer)
        # 讀取下一個資料塊
        chunk = read_next_chunk(data_streamer)
        if isempty(chunk)
            break
        end

        # 累積資料
        append!(accumulated_data, chunk)

        # 如果累積的資料足夠，開始模擬
        if length(accumulated_data) >= window_size + 1
            # 使用滑動視窗進行模擬
            for i in (window_size + 1):length(accumulated_data)
                training_data = accumulated_data[i-window_size:i-1]
                target_draw = accumulated_data[i]

                try
                    selected_numbers, confidence_scores = apply_strategy_selection(strategy, training_data)
                    execution = BacktestExecution(strategy, target_draw.draw_date,
                                                selected_numbers, target_draw, confidence_scores)
                    push!(all_executions, execution)

                catch e
                    @warn "串流模擬失敗: $e"
                    continue
                end
            end

            # 保留最後的視窗資料，移除較舊的資料以節省記憶體
            if length(accumulated_data) > window_size * 2
                accumulated_data = accumulated_data[end-window_size:end]
            end
        end
    end

    @info "串流策略模擬完成: 共 $(length(all_executions)) 次執行"

    return all_executions
end

"""
    create_memory_efficient_windows(data::Vector{LotteryDraw},
                                   window_size::Int,
                                   step_size::Int=1,
                                   memory_limit_mb::Float64=512.0)

創建記憶體高效的回測視窗。

# Arguments
- `data::Vector{LotteryDraw}`: 歷史資料
- `window_size::Int`: 視窗大小
- `step_size::Int`: 步長
- `memory_limit_mb::Float64`: 記憶體限制

# Returns
- `Vector{StreamingBacktestWindow{LotteryDraw}}`: 串流回測視窗
"""
function create_memory_efficient_windows(data::Vector{LotteryDraw},
                                        window_size::Int,
                                        step_size::Int=1,
                                        memory_limit_mb::Float64=512.0)::Vector{StreamingBacktestWindow{LotteryDraw}}
    # 根據記憶體限制調整緩衝區大小
    estimated_item_size = 200  # 估算每個LotteryDraw的記憶體使用量 (bytes)
    max_buffer_items = Int(floor(memory_limit_mb * 1024 * 1024 * 0.1 / estimated_item_size))  # 使用10%的記憶體作為緩衝
    buffer_size = min(max_buffer_items, 1000)  # 最大1000個項目

    @info "創建記憶體高效視窗: 緩衝區大小 $buffer_size"

    return create_streaming_windows(data, window_size, step_size, 1, buffer_size)
end

# ============================================================================
# 平行回測執行 (Task 7.2)
# ============================================================================

"""
    simulate_strategies_parallel(strategies::Vector{SelectionStrategy},
                                historical_data::Vector{LotteryDraw},
                                parallel_config::ParallelConfiguration=configure_parallel_execution())

平行執行多個策略的回測。

# Arguments
- `strategies::Vector{SelectionStrategy}`: 要測試的策略列表
- `historical_data::Vector{LotteryDraw}`: 歷史資料
- `parallel_config::ParallelConfiguration`: 平行處理配置

# Returns
- `Dict{String, Vector{BacktestExecution}}`: 策略名稱到執行結果的映射
"""
function simulate_strategies_parallel(strategies::Vector{SelectionStrategy},
                                     historical_data::Vector{LotteryDraw},
                                     parallel_config::ParallelConfiguration=configure_parallel_execution())::Dict{String, Vector{BacktestExecution}}
    if isempty(strategies)
        throw(ArgumentError("策略列表不能為空"))
    end
    if isempty(historical_data)
        throw(ArgumentError("歷史資料不能為空"))
    end

    @info "開始平行策略回測: $(length(strategies)) 個策略, $(length(historical_data)) 期資料"

    # 創建平行執行器
    executor = ParallelBacktestExecutor(parallel_config)

    # 定義處理函數
    function process_strategy(strategy::SelectionStrategy)
        try
            executions = simulate_strategy(strategy, historical_data)
            return (strategy.name, executions)
        catch e
            @warn "策略 $(strategy.name) 執行失敗: $e"
            return (strategy.name, BacktestExecution[])
        end
    end

    # 執行平行處理
    results = execute_parallel_backtest(executor, strategies, process_strategy)

    # 轉換結果格式
    strategy_results = Dict{String, Vector{BacktestExecution}}()
    for result in results
        if result !== nothing && length(result) == 2
            strategy_name, executions = result
            strategy_results[strategy_name] = executions
        end
    end

    @info "平行策略回測完成: $(length(strategy_results)) 個策略結果"

    return strategy_results
end

"""
    compare_strategies_parallel(strategies::Vector{SelectionStrategy},
                               historical_data::Vector{LotteryDraw},
                               parallel_config::ParallelConfiguration=configure_parallel_execution())

平行執行策略比較。

# Arguments
- `strategies::Vector{SelectionStrategy}`: 要比較的策略列表
- `historical_data::Vector{LotteryDraw}`: 歷史資料
- `parallel_config::ParallelConfiguration`: 平行處理配置

# Returns
- `StrategyComparison`: 策略比較結果
"""
function compare_strategies_parallel(strategies::Vector{SelectionStrategy},
                                   historical_data::Vector{LotteryDraw},
                                   parallel_config::ParallelConfiguration=configure_parallel_execution())::StrategyComparison
    # 執行平行回測
    strategy_results = simulate_strategies_parallel(strategies, historical_data, parallel_config)

    # 計算各策略的指標
    metrics_comparison = Dict{String, Dict{String, Float64}}()

    for (strategy_name, executions) in strategy_results
        if !isempty(executions)
            results = calculate_basic_metrics(executions)
            metrics_comparison[strategy_name] = Dict{String, Float64}(
                "hit_rate" => results.hit_rate,
                "roi" => results.roi,
                "avg_hits_per_execution" => results.average_hits_per_execution,
                "max_consecutive_misses" => Float64(results.max_consecutive_misses),
                "total_executions" => Float64(results.total_executions),
                "total_hits" => Float64(results.total_hits)
            )
        else
            metrics_comparison[strategy_name] = Dict{String, Float64}(
                "hit_rate" => 0.0,
                "roi" => 0.0,
                "avg_hits_per_execution" => 0.0,
                "max_consecutive_misses" => 0.0,
                "total_executions" => 0.0,
                "total_hits" => 0.0
            )
        end
    end

    # 找出最佳策略
    best_strategy = ""
    best_score = -Inf

    for (strategy_name, metrics) in metrics_comparison
        # 綜合評分：ROI權重0.6，命中率權重0.4
        score = metrics["roi"] * 0.6 + metrics["hit_rate"] * 0.4
        if score > best_score
            best_score = score
            best_strategy = strategy_name
        end
    end

    # 創建比較結果
    strategy_names = collect(keys(strategy_results))
    statistical_tests = Dict{String, Float64}()  # 簡化的統計測試

    recommendation = if !isempty(best_strategy)
        "推薦策略: $best_strategy (綜合評分: $(round(best_score, digits=4)))"
    else
        "無法確定最佳策略"
    end

    return StrategyComparison(
        strategy_names,
        metrics_comparison,
        statistical_tests,
        best_strategy,
        recommendation
    )
end

"""
    simulate_strategy_parallel_chunked(strategy::SelectionStrategy,
                                      historical_data::Vector{LotteryDraw},
                                      chunk_size::Int=1000,
                                      parallel_config::ParallelConfiguration=configure_parallel_execution())

使用平行處理和分塊策略執行單一策略回測。

# Arguments
- `strategy::SelectionStrategy`: 要測試的策略
- `historical_data::Vector{LotteryDraw}`: 歷史資料
- `chunk_size::Int`: 分塊大小
- `parallel_config::ParallelConfiguration`: 平行處理配置

# Returns
- `Vector{BacktestExecution}`: 執行結果
"""
function simulate_strategy_parallel_chunked(strategy::SelectionStrategy,
                                           historical_data::Vector{LotteryDraw},
                                           chunk_size::Int=1000,
                                           parallel_config::ParallelConfiguration=configure_parallel_execution())::Vector{BacktestExecution}
    if isempty(historical_data)
        throw(ArgumentError("歷史資料不能為空"))
    end

    @info "開始平行分塊策略回測: $(strategy.name), $(length(historical_data)) 期資料, 分塊大小 $chunk_size"

    # 將歷史資料分塊
    data_chunks = []
    start_idx = 1

    while start_idx <= length(historical_data)
        end_idx = min(start_idx + chunk_size - 1, length(historical_data))
        push!(data_chunks, historical_data[start_idx:end_idx])
        start_idx = end_idx + 1
    end

    # 創建平行執行器
    executor = ParallelBacktestExecutor(parallel_config)

    # 定義處理函數
    function process_chunk(chunk::Vector{LotteryDraw})
        return simulate_chunk(strategy, chunk)
    end

    # 執行平行處理
    chunk_results = execute_parallel_backtest(executor, data_chunks, process_chunk)

    # 合併所有結果
    all_executions = BacktestExecution[]
    for chunk_result in chunk_results
        if chunk_result !== nothing && isa(chunk_result, Vector)
            append!(all_executions, chunk_result)
        end
    end

    @info "平行分塊策略回測完成: $(length(all_executions)) 次執行"

    return all_executions
end

end